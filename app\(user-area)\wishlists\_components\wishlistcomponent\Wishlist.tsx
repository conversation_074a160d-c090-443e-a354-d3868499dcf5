"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import { Building, Utensils, CarFront, IndianRupee, Heart, Star, MapPin, Clock } from "lucide-react";
import { FaIndianRupeeSign } from "react-icons/fa6";
import Link from "next/link";
import Image from "next/image";

const Wishlist = () => {
  const wishlist = [
    {
      label: "Enchanting Ooty",
      category: "Silver",
      image: "https://images.unsplash.com/photo-1544735716-392fe2489ffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      duration: "2N / 3D",
      locations: ["Ooty - 2N", "Coonoor - 1D"],
      price: 8999,
      rating: 4.5,
      hotels: 2,
      meals: 6,
      transport: "Cab",
      description: "Experience the serene beauty of the Queen of Hill Stations"
    },
    {
      label: "Enchanting Manali 6 Day Himalayan",
      category: "Gold",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      duration: "5N / 6D",
      locations: ["Manali - 3N", "Shimla - 2N"],
      price: 15999,
      rating: 4.8,
      hotels: 3,
      meals: 10,
      transport: "Cab",
      description: "Discover the magical landscapes of the Himalayas"
    },
  ];

  if (wishlist.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="w-24 h-24 bg-gradient-to-br from-pink-100 to-red-100 rounded-full flex items-center justify-center mb-6">
          <Heart className="w-10 h-10 text-[#FF5F5F]" />
        </div>
        <h3 className="text-xl font-semibold text-gray-700 mb-2">No Wishlists Yet</h3>
        <p className="text-gray-500 max-w-md">Save your favorite packages and destinations to access them quickly later!</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {wishlist.map((data, index) => (
        <div
          key={index}
          className="group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100 overflow-hidden cursor-pointer"
        >
          {/* Image Container */}
          <div className="relative h-48 overflow-hidden">
            <Image
              src={data.image}
              alt={data.label}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-300"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />

            {/* Category Badge */}
            <div className={`absolute top-4 left-4 px-3 py-1 rounded-full text-xs font-bold text-white shadow-lg ${
              data.category === 'Gold'
                ? 'bg-gradient-to-r from-yellow-400 to-yellow-600'
                : 'bg-gradient-to-r from-gray-400 to-gray-600'
            }`}>
              {data.category}
            </div>

            {/* Heart Icon */}
            <button className="absolute top-4 right-4 p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors duration-200 group">
              <Heart className="w-5 h-5 text-[#FF5F5F] fill-current" />
            </button>

            {/* Duration Badge */}
            <div className="absolute bottom-4 left-4 bg-emerald-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
              {data.duration}
            </div>

            {/* Rating */}
            <div className="absolute bottom-4 right-4 flex items-center gap-1 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span className="text-sm font-semibold text-gray-700">{data.rating}</span>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <h3 className="font-bold text-xl mb-2 text-gray-800 line-clamp-1 group-hover:text-[#FF5F5F] transition-colors duration-200">
              {data.label}
            </h3>

            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {data.description}
            </p>

            {/* Locations */}
            <div className="flex items-center gap-2 mb-4">
              <MapPin className="w-4 h-4 text-[#FF5F5F] flex-shrink-0" />
              <div className="flex flex-wrap gap-2">
                {data.locations.map((location, idx) => (
                  <span key={idx} className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-full">
                    {location}
                  </span>
                ))}
              </div>
            </div>

            {/* Amenities */}
            <div className="flex justify-between items-center mb-4 py-3 bg-gray-50 rounded-lg px-4">
              <div className="flex flex-col items-center">
                <Building className="w-5 h-5 text-gray-500 mb-1" />
                <span className="text-xs text-gray-600 font-medium">{data.hotels} Hotels</span>
              </div>
              <div className="flex flex-col items-center">
                <Utensils className="w-5 h-5 text-gray-500 mb-1" />
                <span className="text-xs text-gray-600 font-medium">{data.meals} Meals</span>
              </div>
              <div className="flex flex-col items-center">
                <CarFront className="w-5 h-5 text-gray-500 mb-1" />
                <span className="text-xs text-gray-600 font-medium">{data.transport}</span>
              </div>
            </div>

            {/* Price and Action */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <FaIndianRupeeSign className="text-[#FF5F5F] text-lg" />
                <span className="text-2xl font-bold text-[#FF5F5F]">
                  {data.price.toLocaleString()}
                </span>
                <span className="text-sm text-gray-500 ml-1">per person</span>
              </div>

              <button className="bg-gradient-to-r from-[#FF5F5F] to-[#FF9080] text-white px-6 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                Book Now
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Wishlist;

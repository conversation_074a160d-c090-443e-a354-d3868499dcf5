import React from 'react';
import { Users, Minus, Plus } from 'lucide-react';

interface WhoPanelProps {
  guests: number;
  onGuestsChange: (guests: number) => void;
}

export const WhoPanel: React.FC<WhoPanelProps> = ({ guests, onGuestsChange }) => {
  const handleIncrement = () => {
    onGuestsChange(guests + 1);
  };

  const handleDecrement = () => {
    if (guests > 0) {
      onGuestsChange(guests - 1);
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Who's coming?</h3>
      
      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
        <div className="flex items-center gap-3">
          <Users className="w-5 h-5 text-gray-400" />
          <div>
            <div className="font-medium text-gray-900">Guests</div>
            <div className="text-sm text-gray-500">Add travelers</div>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <button
            onClick={handleDecrement}
            disabled={guests <= 0}
            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Minus className="w-4 h-4" />
          </button>
          
          <span className="w-8 text-center font-medium">{guests}</span>
          
          <button
            onClick={handleIncrement}
            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-400 transition-colors"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div className="text-xs text-gray-500 mt-2">
        You can add more details later
      </div>
    </div>
  );
};

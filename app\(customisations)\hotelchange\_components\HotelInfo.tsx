"use client"
import React from 'react'
import { useAppSelector } from '@/app/store/store'
import HotelInfoList from './HotelInfoList'
interface Filter
{
    name:string,
    days:number,
    nights:number,
    price:number,
    place1:string,
    place2?:string,
    filter_category:string,
    room_type:string,
    cost:string
}

const FilterCardListData:Filter[]=[
    {
        name: 'Manali Budget',
        days: 2,
        nights: 3,
        price: 8999,
        place1:"Manli",
     filter_category:'5',
     room_type:"Ac with Balcony",
     cost:"10,199"
        
    },
    {
        name: 'Delhi ',
        days: 2,
        nights: 3,
        price: 7999,
        place1:"Manali",
        place2:"shimla",
     filter_category:'4',
     room_type:"Super Deluxe",
     cost:'5,999'
        
    },
    {
        name: 'Enchanting Manali',
        days: 2,
        nights: 3,
        price: 4999,
        place1:"Manali",
        place2:"shimla",
     filter_category:'3',
     room_type:"Premium",
     cost:"6,999"
        
    },
    {
        name: 'Manali Budget',
        days: 2,
        nights: 3,
        price: 5999,
        place1:"Manali",
        place2:"shimla",
     filter_category:'Pool',
     room_type:"Superior Room",
     cost:"8,000"
        
    },
    {
        name: 'Rajastan Budget',
        days: 6,
        nights: 5,
        price: 6999,
        place1:"Manali",
        room_type:"Non-Ac",
        cost:"10,000",
       
     filter_category:'5',
        
    },
    {
        name: 'Enchanting Rajasthan ',
        days: 2,
        nights: 3,
        price: 5999,
        place1:"Manali",
        place2:"shimla",
     filter_category:'4',
     room_type:"Ac with Balcony",
     cost:"5,000"
        
    },
    {
        name: 'Enchanting Ooty  ',
        days: 2,
        nights: 3,
        price: 3499,
        place1:"Manali",
     filter_category:'3',
     room_type:"Ac with Balcony",
     cost:"4,000"
        
    }, {
        name: 'Enchanting Manali ',
        days: 3,
        nights: 2,
        price: 4999,
        place1:"Manali",
        place2:"shimla",
     filter_category:'5',
     room_type:"Ac with Balcony",
     cost:"4,000"
        
    },
    {
        name: 'Kodaikanal Budget',
        days: 3,
        nights: 2,
        price: 2199,
        place1:"Manali",
     filter_category:'Pool',
     room_type:"Ac with Balcony",
     cost:"10,199"
        
    },
    {
        name: 'Kodaikanal Budget',
        days: 3,
        nights: 2,
        price: 2199,
        place1:"Manali",
     filter_category:'Pool',
     room_type:"Ac with Balcony",
     cost:"10,199"
        
    },
]
const FilterCards = () => {
    const filterCategory = useAppSelector((state)=>state.filterCategory.filterCategory)
    const filterData= FilterCardListData.filter(
        (pkg) => pkg.filter_category === filterCategory
      );
    
   
  return (
    <div className='carousel-container gap-5 flex-col items-center mt-4'>
    {
        filterData.map((pkg,index)=>(
           
            <HotelInfoList
            name={pkg.name}
            days = {pkg.days}
            nights={pkg.nights}
            price={pkg.price}
            place1={pkg.place1}
            place2={pkg.place2}
            filter_category={pkg.filter_category}
            room_type={pkg.room_type}
            cost={pkg.cost}
            key={index}
            />
        ))
    }

</div>
  )
}

export default FilterCards
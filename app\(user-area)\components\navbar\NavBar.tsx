"use client";
import useRoutes from "@/app/hooks/useRoutes";
import React, { useEffect, useState } from "react";
import NavItems from "./NavItems";
import NavMenu from "./NavMenu";
import { UserCircleIcon } from "lucide-react";
import Image from "next/image";
import { usePathname } from "next/navigation";

const accountNav = {
  label: "Account",
  icon: UserCircleIcon,
  href: "/account",
};

const DesktopNavBar = () => {
  const routes = useRoutes();
  const pathname = usePathname();
  const [solid, setSolid] = useState(false);

  useEffect(() => {
    const hero = document.getElementById("hero-section");

    // Check if we're on the home page (which has hero section)
    const isHomePage = pathname === "/";

    // If not on home page, always make navbar solid
    if (!isHomePage) {
      setSolid(true);
      return;
    }

    // For home page, use hero section intersection logic
    if (!hero) {
      const onScroll = () => setSolid(window.scrollY > 40);
      window.addEventListener("scroll", onScroll, { passive: true });
      onScroll();
      return () => window.removeEventListener("scroll", onScroll);
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        // When hero is NOT visible, make navbar solid
        setSolid(!entry.isIntersecting);
      },
      { rootMargin: "-64px 0px 0px 0px", threshold: 0.01 }
    );

    observer.observe(hero);
    return () => observer.disconnect();
  }, [pathname]);

  // Determine background styling based on page and solid state
  const getNavbarStyles = () => {
    const isHomePage = pathname === "/";

    if (isHomePage) {
      // Home page: transparent when over hero, solid when scrolled
      if (solid) {
        return {
          containerClasses: "bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/90 shadow-lg border-b border-gray-100",
          gradientClasses: ""
        };
      } else {
        return {
          containerClasses: "bg-transparent",
          gradientClasses: "bg-gradient-to-b from-black/50 to-transparent"
        };
      }
    } else {
      // Other pages: always solid white background
      return {
        containerClasses: "bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/90 shadow-lg border-b border-gray-100",
        gradientClasses: ""
      };
    }
  };

  const { containerClasses, gradientClasses } = getNavbarStyles();

  return (
    <div
      className={`hidden lg:block top-0 z-40 w-full fixed p-3 px-10 transition-all duration-300 ${containerClasses} ${gradientClasses}`}
    >
      <div className="flex justify-between items-center ">
        <div className="flex-1">
          <Image
            src="/logo.png" // Make sure to add your logo image in the public/images folder
            alt="Tripxplo Logo"
            width={130} // Adjust the width as needed
            height={45} // Adjust the height as needed
            priority
            className="mx-auto"
            style={{ filter: "brightness(1)", marginLeft: '235px' }}
          />
        </div>
        <div className="flex space-x-8 items-center" style={{ marginRight: '167px' }}>
          {routes.map((route) => (
            <NavItems
              key={route.label}
              label={route.label}
              href={route.href}
              active={route.active}
              light={!solid}
            />
          ))}
          <div className={solid ? "text-slate-600" : "text-white"}>
            <NavMenu label={""} icon={accountNav.icon} href={accountNav.href} />
        </div>
      </div>
    </div>
    </div>
  );
};

export default DesktopNavBar;

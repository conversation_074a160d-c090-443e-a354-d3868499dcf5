"use client";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import BookingDetails from "./BookingDetails";
import { useState,useEffect } from "react";
import PackagesLoadingFull from "@/app/(user-area)/components/loading/PackagesLoadingFull";
import { bookingStatus } from "@/app/utils/api/bookingStatus";
  
import React from "react";
const Booking = () => {
  const router = useRouter();
  function clickBack() {
    router.push("/mybookings");
  }
  const [data,setData] = useState<any>(null);
  const [verified,setVerified] = useState(false);
  let timer: string | number | NodeJS.Timeout | undefined;
  const getBookingDetails = async () =>{
    const urlSpilt = window.location.pathname.split("/")
    const id = urlSpilt.pop();
    console.log("id : ",id);
    const response = await bookingStatus(id as string);
    if(response?.data.success){
    setData(response?.data?.result);
    console.log("data : ",response?.data?.result);
    clearTimeout(timer);
    setVerified(true);
    }else {
      timer = setTimeout(getBookingDetails, 500);
    }
  }
  
  useEffect(() => {
    getBookingDetails();
  }, []);
  if(!verified){
    return <PackagesLoadingFull />
  }
  const events = [
    {
      heading: "Booking Confirmed",
      subHeading: "Silver Package",
    },
    {
      heading: "Partially Paid",
      subHeading: "Next payment before "+data?.fullStartDate,
    },
    {
      heading: "Balance Payment",
      subHeading: "Full Payment before due date",
    },
  ];

  return (
    <>
      <section className="">
        <div
          className="fixed top-0 text-center flex items-center  w-full h-auto py-5 lg:py-10 bg-white z-10"
          style={{ boxShadow: "0px 4px 36.1px 0px rgba(190, 190, 190, 0.22)" }}
        >
          <span className="pl-4 lg:pl-6 flex items-center">
            <button onClick={clickBack}>
              <ArrowLeft className="h-[30px] w-[30px] text-[#FF5F5F]" />
            </button>
          </span>
          <h1
            className="text-center flex flex-wrap px-2 h-auto font-Poppins text-[18px] not-italic font-semibold leading-normal tracking-[0.18px]"
            style={{
              textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.20)",
              backgroundImage:
                "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
              backgroundClip: "text",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            Booking Details
          </h1>
        </div>
      </section>
      <div className="flex flex-col items-center  py-20 ">
        <BookingDetails pack={data} events={events} />
      </div>
      {/* <Book /> */}
    </>
  );
};

export default Booking;
'use client';
import * as yup from 'yup';
import { useAuth } from '@/app/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { login } from '@/app/utils/api/login';
import toast from 'react-hot-toast';
import GoogleSignIn from '@/components/ui/gogglesignin';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';

const Login = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  // const { isAuthenticated, isLoading } = useAuth(); // Use isLoading from useAuth

  // useEffect(() => {
  //   if (isAuthenticated && !isLoading) {
  //     router.push('/');
  //   }
  // }, [isAuthenticated, router, isLoading]);

  const loginSchema = yup.object().shape({
    email: yup
      .string()
      .email('Invalid Email Address')
      .required('Email is required'),
    password: yup.string().required('Password is required'),
  });
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: yupResolver(loginSchema),
    mode: 'onBlur',
    reValidateMode: 'onChange',
    shouldFocusError: true,
  });

  const onSubmit = async (values: yup.InferType<typeof loginSchema>) => {
    try {
      setLoading(true);
      const response = await login(values.email, values.password);
      localStorage.setItem('accessToken', response.data.result.accessToken);
      localStorage.setItem('refreshToken', response.data.result.refreshToken);
      toast.success('Login Successful');
      const url = localStorage.getItem('currentPath') || '/';
      localStorage.removeItem('currentPath');
      router.push(url);
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Invalid email or password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex items-center justify-center min-h-screen'>
      <div className='sm:px-5 sm:pt-5 lg:w-3/5'>
        <h1 className='text-[#FF5F5F] font-semibold text-2xl mb-6 text-center'>
          TripXplo
        </h1>
        <h1 className='text-left text-neutral-500 text-lg font-medium'>
          Login to your account
        </h1>

        <GoogleSignIn />

        <div className='flex items-center justify-center my-4'>
          <div className='flex-grow border-t border-gray-300'></div>
          <span className='mx-4 text-gray-500'>or</span>
          <div className='flex-grow border-t border-gray-300'></div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className='grid grid-cols-1 gap-2 mt-10'>
            <div className='form-group mb-4'>
              <input
                type='email'
                id='email'
                {...register('email')}
                className={`input-field ${errors.email ? 'input-error' : ''}`}
                placeholder=' '
              />
              <label htmlFor='email' className='input-label'>
                Email
              </label>
              {errors.email && (
                <p className='error-message'>{errors.email.message}</p>
              )}
            </div>
            <div className='form-group mb-4'>
              <input
                type='password'
                id='password'
                {...register('password')}
                className={`input-field ${
                  errors.password ? 'input-error' : ''
                }`}
                placeholder=' '
              />
              <label htmlFor='password' className='input-label'>
                Password
              </label>
              {errors.password && (
                <p className='error-message'>{errors.password.message}</p>
              )}
            </div>
          </div>

          <div className='flex items-center justify-center mt-6'>
            <button
              type='submit'
              className='bg-gradient-to-r shadow-sm from-[#FF5F5F] to-[#ff5b5b] hover:bg-[#FF5F5F]/90 w-full text-white font-medium py-3 px-4 rounded focus:outline-none'
              disabled={loading}
            >
              {loading ? 'Logging in...' : 'Login'}
            </button>
          </div>
        </form>

        <div className='mt-4 text-muted-foreground text-sm w-full flex justify-center'>
          <h1 className=''>
            New user?{' '}
            <span
              onClick={() => router.push('/register')} // TODO: register page redirect
              className='text-[#ff5b5b] underline decoration-[#ff5b5b] pl-1 font-semibold cursor-pointer'
            >
              Register
            </span>
          </h1>
        </div>
      </div>
    </div>
  );
};

export default Login;

"use client";
import { Coins, HandCoins, Heart, Home, UserCircleIcon } from "lucide-react";
import React from "react";
import NavMenu from "./NavMenu";

const navMenu = [
  {
    label: "Home",
    icon: Home,
    href: "/",
  },

  {
    label: "Rewards",
    icon: HandCoi<PERSON>,
    href: "/rewards",
  },
  {
    label: "Account",
    icon: UserCircleIcon,
    href: "/account",
  },
];

const NavBar = () => {
  return (
    <div className="lg:hidden bottom-0 left-0 px-5 py-2 fixed w-full h-18 border-slate-200 bg-white white-shadow z-40">
      <div className="flex items-center justify-center pb-2">
        <div className="w-4/6 h-1 -mt-4 bg-gradient-to-r from-[#FF5F5F] to-[#FF9080] rounded-full"></div>
      </div>
      <div className="h-full flex justify-evenly space-x-6 items-center ">
        {navMenu.map((nav, index) => (
          <div key={index}>
            <NavMenu label={nav.label} icon={nav.icon} href={nav.href} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default NavBar;

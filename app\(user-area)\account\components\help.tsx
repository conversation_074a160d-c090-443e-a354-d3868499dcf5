"use client"
import { <PERSON><PERSON><PERSON><PERSON>og, AlertDialogCancel, AlertDialogContent, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Headphones } from 'lucide-react'
import Link from 'next/link'
import React from 'react'

const Help = () => {
  return (
   <AlertDialog>
    <AlertDialogTrigger>
    <div className="w-full h-16 text-slate-500 cursor-pointer border rounded-lg flex space-x-6 items-center p-2">
    <Headphones className="text-[#ff9080]" />
    <h1>Help</h1>
  </div>
    </AlertDialogTrigger>

    <AlertDialogContent className='w-4/5'>
    <div className="flex flex-col">
<h1 className='text-[#FF7865] text-xl font-semibold'>Contact us</h1>
        <h1 className="mt-4 text-grey-600">
        For any inquiries or support regarding your vacation plans, feel free to reach out to our dedicated team at <a  className="underline text-grey-700 font-medium"href={"mailto:<EMAIL>"}><EMAIL>.</a>
        </h1>
        <p className='mt-3'>We aim to respond to all emails within <span className='text-grey-700 underline drop-shadow-black drop-shadow-sm'>24-48 hours</span> </p>
        <p className='mt-2'>Thank you for choosing <span className='text-[#FF5F5F] drop-shadow-black drop-shadow-sm'>TripXplo</span>. We&apos;re here to make your travel experience smooth and enjoyable!</p>
    </div>
<AlertDialogCancel>
    Close
</AlertDialogCancel>

    </AlertDialogContent>
   </AlertDialog>
  )
}

export default Help
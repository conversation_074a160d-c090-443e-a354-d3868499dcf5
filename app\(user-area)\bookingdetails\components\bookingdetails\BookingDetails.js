import React from "react";
import { FaIndianRupeeSign } from "react-icons/fa6";
import { CalendarDays } from "lucide-react";
import { Check } from "lucide-react";
import { Circle } from "lucide-react";

const BookingDetails = ({ events }) => {
    return (
        <div className="w-[370px] h-[440px] rounded-[16px] bg-white" style={{ border: "var(--adult, 2px) solid rgba(172, 161, 159, 0.34)", boxShadow: "4px 8px 26.3px 0px rgba(172, 161, 159, 0.18)" }}>
            <h1 className="text-[#5D6670] mt-[16px] flex justify-center font-Poppins text-[12px] font-semibold leading-normal tracking-[0.12px]">Booking ID : 5NMAN04SL0201</h1>
            <hr className="w-[278px] mx-auto mt-[12px] flex-shrink-0 stroke-[1px]" style={{ stroke: "rgba(0, 0, 0, 0.08)" }} />
            <section className="mt-[16px] flex">
                <p className="flex ml-[21px] font-Poppins justify-center items-center w-[49px] h-[19px] px-[2px] rounded-8/2 shadow-goldShadow text-white text-[9px] font-semibold tracking-[1px] bg-silverGradient" style={{ textShadow: "0px 0.5px 0.5px rgba(0, 0, 0, 0.12)" }}> Silver</p>
                <p className='w-[37px] h-[16px] ml-auto mr-[23px] rounded-[7px] bg-[#1EC089] flex items-center justify-center text-white text-[7px] font-semibold leading-normal font-inter'>3N / 4D</p>
            </section>
            <section className="w-full flex mt-[16px] ">
                <span className="ml-[23px] not-italic w-1/4 text-[12px] font-semibold leading-normal bg-clip-text text-transparent" style={{ backgroundImage: "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)" }}>Enchanting Manali 6 Day Himalayan</span>
                <span className="flex ml-auto mr-[25px] items-center text-[#6A778B] font-montserrat text-[16px] not-italic font-bold leading-[21.6px] tracking-[0.48px]"><FaIndianRupeeSign className="w-[10px] h-[14px] pr-[2px]" />8,999 </span>
            </section>
           
                <div className="mt-[17px] flex items-center justify-between rounded-[12px] w-[295px] h-[44px]" style={{ background: "rgba(172, 161, 159, 0.08)" }}>
                    <p className=" ml-[15px] flex items-center font-Poppins  text-[#5D6670] text-[8px] not-italic font-semibold leading-normal"><CalendarDays className="text-[#FF7865] drop-shadow-pkgdoneShadow mr-[7px]" size={15} /> Wed, 12 Jun - Fri, 14 Jun</p>
                    <button className=" mr-[40px] inline-flex items-center w-[112px] h-[22px] px-[12px] py-[3.5px] rounded-lg bg-myBookingsBookingDoneBg text-white text-[10px] font-medium leading-normal tracking-[0.1px]"><Check size={12} className="text-white mr-[4px]" /> Booking Done</button>
                </div>
            
            <section className="mt-[26px] ml-[23px]"> 
                <div className="mt-[26px]">
               
                    {events.map((event, key) => (
                        <>
                        <div key={key} className="flex  " >
                            <div className="flex flex-col items-center ">
                            <Circle size={12} style={{ fill: "linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)","background":"green-400" }} />
                        {key!== events.length-1   && 
                        <div className="h-[40px] " style={{ borderLeft: "2px solid #1EC089" }}> </div>}
                            </div>

                            
                                <EventCards heading={event.heading} subHeading={event.subHeading} />
                            </div>
                       
                       
                     
                       
                        </>
                    ))}
          
                </div>
               
            </section> 
        </div>
    );
};

const EventCards = ({ heading, subHeading }) => {
    return (
        <div className="flex flex-col ml-[13px]  ">
            <div className="font-Poppins  text-[12px] not-italic font-semibold leading-normal tracking-[0.12px]  bg-clip-text text-transparent" style={{"backgroundImage": " linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)"}}>{heading}</div>
            <div className="text-[#8391A1] font-Poppins text-[10px] font-normal not-italic leading-normal tracking-[0.1px]">{subHeading}</div>
        </div>
    );
};




export default BookingDetails;
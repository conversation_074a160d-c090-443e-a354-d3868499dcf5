"use client";

import { setReplaceCab } from "@/app/store/features/cabChangeSlice";
import { Vehicle } from "@/app/types/pack";
import { VehicleDetail } from "@/app/types/vehicle";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import { Pencil } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";

export default function Cab({ vehicle }: { vehicle: VehicleDetail }) {
  const router = useRouter();
  const dispatch = useDispatch();
  const packId = useSelector((store: any) => store.package.data?.packageId);

  // function changeCab() {
  //   router.push("/cabchange");
  // }

  function changeCab() {
    dispatch(setReplaceCab(vehicle));
    router.push(`${packId}/cabchange`);
  }

  return (
    <div className="mt-12 lg:w-1/2">
      <div className="flex justify-between items-center">
        <div>
          <p
            className="text-[20px] lg:text-[30px] font-semibold bg-clip-text text-transparent leading-normal whitespace-nowrap tracking-[0.18px]"
            style={{
              backgroundImage:
                "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
              textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.13)",
            }}
          >
            Cab - Transfer
          </p>
          <h1 className="mt-1 text-neutral-600 text-xs lg:text-base">
            Cab Included in Your Package
          </h1>
        </div>
        <button
          onClick={changeCab}
          className="flex gap-1 h-[31px] justify-center leading-normal tracking-[0.18px] mb-2 items-center px-2 py-2 whitespace-nowrap bg-gradient-to-r cursor-pointer from-[#8391A1] to-[#5D6670] rounded-[14px] text-white text-[10px] not-italic font-semibold"
        >
          <Pencil size={12} /> <h1 className="cursor-pointer">Change Cab</h1>
        </button>
      </div>
      <div
        className="flex flex-col mt-8 w-full lg:w-4/5 mx-auto lg:mx-0 rounded-[14px] bg-white border-[1.5px] border-[#FF7865] p-2 lg:p-4"
        style={{ boxShadow: "2px 4px 19.9px 0px rgba(255, 120, 101, 0.20)" }}
      >
        <div className="flex lg:flex-col justify-between items-center">
          <Image
            src={NEXT_PUBLIC_IMAGE_URL + vehicle?.image}
            height={87}
            width={87}
            alt="Cab"
            className="my-[33px] mx-[24px] lg:my-[20px] lg:h-[100px] lg:w-[180px] object-contain"
          />

          <div className="flex flex-col lg:items-center">
            <p
              className="mt-[13px] h-[21px] lg:h-auto text-[14px] lg:text-[20px] font-semibold leading-normal tracking-[0.14px] bg-clip-text text-transparent"
              style={{
                textShadow: "2px 4px 8.4px rgba(51, 214, 159, 0.12)",
                backgroundImage:
                  "linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)",
              }}
            >
              {vehicle?.vehicleName}
            </p>
            <p className="text-[#5D6670] text-[12px] lg:text-[16px] font-normal leading-normal">
              {vehicle?.vehicleCompany} - {vehicle?.acType ? "AC" : "Non-AC"}
            </p>
            <p
              className="mt-[11px] text-[12px] lg:text-[16px] font-medium leading-normal bg-clip-text text-transparent"
              style={{
                backgroundImage:
                  "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
              }}
            >
              {vehicle?.inclusion.map((i, index) => (
                <span key={index}>
                  {" "}
                  {i}{" "}
                  {index === vehicle?.inclusion.length - 1 ? (
                    ""
                  ) : (
                    <span className="text-neutral-400 font-extralight">|</span>
                  )}
                </span>
              ))}
            </p>
            <div className="flex lg:mt-4 items-center gap-2 lg:gap-4 whitespace-nowrap">
              <div className="flex items-center justify-center border gap-1 lg:gap-2 border-neutral-200 px-2 lg:px-4 py-1 lg:py-2 shadow-sm rounded-lg">
                <Image
                  src="/Seat.svg"
                  width={12}
                  height={12}
                  alt="seat"
                  className="lg:w-[16px] lg:h-[16px]"
                />
                <p className="h-[12px] lg:h-auto text-[#5D6670] text-[10px] lg:text-[14px] font-medium">
                  {vehicle?.seater} Seater
                </p>
              </div>
              <div className="flex items-center border justify-center gap-1 lg:gap-2 border-neutral-200 px-2 lg:px-4 py-1 lg:py-2 shadow-sm rounded-lg whitespace-nowrap">
                <Image
                  src="/Luggage.svg"
                  width={12}
                  height={12}
                  alt="luggage"
                  className="lg:w-[16px] lg:h-[16px]"
                />
                <p className="h-[12px] lg:h-auto text-[#5D6670] text-[10px] lg:text-[14px] font-medium">
                  {vehicle?.luggage} Luggage
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";
import Image from "next/image";
import Link from "next/link";
import {
  Building,
  Utensils,
  CarFront,
  IndianRupee,
  ArrowLeft,
  CalendarDays,
  Check,
  CircleUserRound,
  UtensilsIcon,
  MapPin,
  Calendar,
  Clock,
  Router,
  ChevronLeft,
  BedSingle,
  Hotel,
  Car,
  User,
  UserRound,
  TagsIcon,
} from "lucide-react";
import { X } from "lucide-react";

import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { PiFlagPennantLight } from "react-icons/pi";
import { Tags } from "lucide-react";
import api from "../../../../utils/api/auth";
// import PaymentOptions from "./PaymentOptions";
import { ChevronDown } from "lucide-react";
import { FaIndianRupeeSign } from "react-icons/fa6";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { PackType } from "@/app/types/pack";
import FareBreakup from "../../_components/FareBreakup";
import toast from "react-hot-toast";
import { BookingPayloadType } from "@/app/types/BookingPayload";
import { DateDestination, Room } from "@/app/hooks/usePackageList";
import { createBooking } from "@/app/utils/api/createBooking";
import { HandCoins } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { formatIndianNumber } from "@/lib/utils";
import { LuDices } from "react-icons/lu";

import { Coupon } from "@/app/types";
import { useAppSelector } from "@/app/store/store";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import CouponSelect from "./CouponSelect";
import { getCoupon } from "@/app/utils/api/getCoupon";
// import { useRouter } from "next/navigation";
export default function PackageBooking() {
  const router = useRouter();
  const [packageCoupons, setPackageCoupons] = useState<Coupon[]>([]);
  useEffect(() => {
    const fetchCoupons = async () => {
      const coupons = await api.get("package/coupon");

      setPackageCoupons(coupons.data?.result);
      // console.log(packageCoupons, "coupons");
      console.log(coupons.data?.result, "data coupons");
    };

    fetchCoupons();
  }, []);
  const availableRedeemCoins = useSelector(
    (store: any) => store.userSlice.redeemCoins
  );

  const [packageId, setPackageId] = useState<string>("");
  const pack: PackType = useSelector((store: any) => store.package.data);
  const [selected, setSelected] = useState(false);
  const [showAllCoupons, setShowAllCoupons] = useState<boolean>(false);
  const [selectedCouponId, setSelectedCouponId] = useState<string | null>("");
  const selectedCoupons = packageCoupons.find(
    (c) => c.couponId === selectedCouponId
  );
  const [isCouponApplied, setIsCouponApplied] = useState(false);
  const [isRedeemApplied, setIsRedeemApplied] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [showCouponStatus, setShowCouponStatus] = useState(false);
  const [couponValue, setCouponValue] = useState(0);
  const [disableCouponInput, setDisableCouponInput] = useState(false);
  const [disableRedeemInput, setDisableRedeemInput] = useState(false);

  const [option, setOption] = useState<string>("");
  const [showCoupon, setShowCoupon] = useState(false);

  const [coupon, setCoupon] = useState<string | null>(null);
  const [redeem, setRedeem] = useState<number>(0);

  const { currentPackageId } = useAppSelector((store) => store.packageDetails);
  const roomCapacityData: Room = useSelector(
    (store: any) => store.roomSelect.room
  );

  const dateAndDestination: DateDestination = useSelector(
    (store: any) => store.searchPackage
  );
  useEffect(() => {
    if (option == "full") {
      setShowCoupon(true);
    } else if (option == "advance") {
      setShowCoupon(false);
    }
  }, [option]);
  useEffect(() => {
    const url = window.location.pathname;
    const splitUrl = url.split("/");
    if (splitUrl.length > 2) {
      setPackageId(splitUrl[2]);
    }
  }, []);
  function handleChangeRadio(opt: string) {
    setOption(opt);
    setSelected(true);
  }

  async function handleBooking() {
    if (!selected) {
      toast.error("please select payment option");
      return;
    }
    const extraAdult =
      roomCapacityData.totalAdults -
      roomCapacityData.totalRooms * roomCapacityData.perRoom;
    const noAdult =
      extraAdult > 0
        ? roomCapacityData?.totalAdults - extraAdult
        : roomCapacityData?.totalAdults;

    const payload: BookingPayloadType = {
      startDate: dateAndDestination.date?.slice(0, 10),
      paymentType: option,
      redeemCoin: redeem,
      noAdult: noAdult,
      noChild: roomCapacityData.totalChilds,
      noRoomCount: roomCapacityData.totalRooms,
      noExtraAdult: extraAdult > 0 ? extraAdult : 0,
      couponCode: option === "full" ? (coupon ? coupon : null) : null,
      packageId: pack?.packageId,
      activity: pack?.activity,
      hotelMeal: pack?.hotelMeal,
      fullStartDate: pack?.fullStartDate,
      fullEndDate: pack?.fullEndDate,
      checkStartDate: pack?.checkStartDate,
      checkEndDate: pack?.checkEndDate,
      vehicleDetail: pack?.vehicleDetail,
    };
    const response = await createBooking(payload);
    console.log("create booking : ", response);
    console.log(
      "create booking url : ",
      response.data.paymentLink.data.instrumentResponse.redirectInfo.url
    );
    if (
      response?.data?.paymentLink?.data?.instrumentResponse?.redirectInfo?.url
    ) {
      router.push(
        response?.data?.paymentLink?.data?.instrumentResponse?.redirectInfo?.url
      );
    } else {
      router.back();
    }
  }
  function handleApply(msg: string) {
    toast.success(msg);
    setIsRedeemApplied(true);
    setDisableRedeemInput(true);
  }

  const handleApplyCoupon = (id: string) => {
    setSelectedCouponId(id);
    setShowAllCoupons(false);
  };
  const handleRemoveCoupon = () => {
    setSelectedCouponId(null);
  };
  function handleRedeemCoins(coins: number) {
    if (coins <= availableRedeemCoins) {
      setRedeem(coins);

      console.log(coins);
    }
  }
  async function handleCouponApply(coupon: string | null) {
    if (coupon) {
      setDisableCouponInput(true);
      try {
        const couponResponse = await getCoupon(coupon.trim());
        toast.success("Coupon applied successfully");

        setShowCouponStatus(true);
        if (couponResponse?.result?.valueType == "percentage") {
          setCouponValue(
            (couponResponse?.result?.value * pack?.finalPackagePrice) / 100
          );
        } else {
          setCouponValue(couponResponse?.result?.value);
        }
        setIsCouponApplied(true);
      } catch (e) {
        setDisableCouponInput(false);
      }
    } else {
      toast.error("Please enter coupon code");
    }
  }
  console.log(showAllCoupons);
  const RemoveCoupon = () => {
    setCoupon(null);
    setDisableCouponInput(false);
    setIsCouponApplied(false);
    setShowCouponStatus(false);
    setCouponValue(0);

    setTimeout(() => {
      toast.success("Coupon Removed");
    }, 300);
  };
  const closeAlert = () => {
    setShowAlert(false);
  };
  const showMore = () => {
    setShowAlert(true);
    console.log(showAlert);
  };
  const removeRedeem = () => {
    setRedeem(0);
    setIsRedeemApplied(false);
    setDisableRedeemInput(false);
    toast.success("Redeem Removed");
  };

  return (
    <div>
      <div className="flex items-center pl-3 mt-4  gap-6">
        <div
          className="p-2 sm:block  border border-neutral-100 shadow-sm rounded-md flex justify-center cursor-pointer"
          onClick={() => router.push(`/package/${currentPackageId}`)}
        >
          <ChevronLeft size={24} className="text-[#FF5F5F]" />
        </div>

        <div className="flex flex-col justify-center">
          <header className="font-Poppins text-[20px] lg:text-[28px] font-semibold text-[#FF5F5F] drop-shadow-black drop-shadow-sm">
            Booking Details
          </header>
          <h1 className="text-neutral-700 text-[16px] lg:text-[18px] font-medium">
            {pack.packageName}
          </h1>
        </div>
      </div>
      <div className="mt-10">
        <h1 className="hidden lg:block mb-[15px] lg:mt-7 text-[#5D6670] text-center font-Poppins text-[18px] lg:text-[30px] font-semibold not-italic leading-normal tracking-[0.18px]">
          Your Package Booking
        </h1>
      </div>
      <div className="pt-8 pb-40 lg:pb-32 px-5">
        <div className="max-w-7xl mx-auto lg:flex lg:gap-8">
          {/* Package Details Section */}
          <div className="lg:flex-1">
            <div className="h-auto border border-neutral-200 w-full max-w-4xl mx-auto py-5 px-5 lg:px-8 bg-[#FFF] rounded-[14px] shadow-sm">
              {/* <p className="text-[#5D6670] text-xs font-medium font-Poppins flex items-cente justify-center">
              Booking Id : 5NMAN04SL0201
            </p> */}

              <hr
                className="my-3 px-4"
                style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
              />
              <div className="flex space-x-3 lg:space-x-6 items-start lg:items-center">
                <div className="h-28 w-28 lg:h-40 lg:w-40 relative rounded-lg flex-shrink-0">
                  <Image
                    src={
                      NEXT_PUBLIC_IMAGE_URL +
                      (pack?.packageImg?.length ? pack?.packageImg[0] : "")
                    }
                    fill
                    className="object-cover rounded-lg"
                    alt={pack?.packageName}
                  />
                </div>

                <div className="flex flex-col space-y-3">
                  <h1 className="text-[15px] lg:text-[18px] text-[#FF5F5F] drop-shadow-black drop-shadow-sm font-semibold">
                    {pack?.packageName}
                  </h1>

                  <div className="flex items-center justify-center lg:justify-start space-x-4 lg:space-x-6">
                    {/* Hotel and Car icons section */}
                    <h1 className="flex text-slate-500 flex-col items-center space-y-1 text-[12px] lg:text-[14px]">
                      <Hotel size={16} className="lg:w-6 lg:h-6" />
                      <span className="text-[10px] lg:text-[12px]">
                        {pack.hotelCount > 1
                          ? pack.hotelCount + " Hotels"
                          : pack.hotelCount + " Hotel"}
                      </span>
                    </h1>
                    <h1 className="flex text-slate-500 flex-col items-center space-y-1 text-[12px] lg:text-[14px]">
                      <Car size={16} className="lg:w-6 lg:h-6" />
                      <span className="text-[10px] lg:text-[12px]">
                        {pack.vehicleCount > 1
                          ? pack.vehicleCount + " Cabs"
                          : pack.vehicleCount + " Cab"}
                      </span>
                    </h1>
                  </div>
                </div>
              </div>
              <hr
                className="my-3 px-4"
                style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
              />

              <div className="w-full">
                <div className="lg:grid lg:grid-cols-2 lg:gap-6">
                  <div className="flex flex-row items-center flex-wrap gap-5 lg:gap-4">
                    {/* Destination Info */}
                    <div className="flex items-center gap-1 lg:gap-2">
                      <div className="p-1 lg:p-2 border border-neutral-100 shadow-sm rounded-lg">
                        <MapPin
                          size={14}
                          className="text-[#FF5F5F] lg:w-5 lg:h-5"
                        />
                      </div>
                      <div className="flex items-center gap-1 lg:gap-2">
                        {pack?.destination
                          ?.filter((dest) => dest.noOfNight > 0)
                          ?.map((dest, i, arr) => (
                            <div
                              className="flex items-center text-nowrap"
                              key={i}
                            >
                              <div className="flex items-center text-[#FF7865] font-Poppins text-[12px] lg:text-[14px] font-medium">
                                &nbsp;{dest?.noOfNight}N
                              </div>
                              <div className="flex items-center text-[#6A778B] font-Poppins text-[12px] lg:text-[14px] font-medium">
                                &nbsp;-&nbsp;{dest?.destinationName}
                              </div>
                              {i !== arr.length - 1 && (
                                <span className="text-[#6A778B]">
                                  &nbsp;|&nbsp;
                                </span>
                              )}
                            </div>
                          ))}
                      </div>
                    </div>

                    <div className="flex items-center gap-1 lg:gap-2">
                      <div className="p-1 lg:p-2 border border-neutral-100 shadow-sm rounded-lg">
                        <Clock
                          size={14}
                          className="text-[#FF5F5F] lg:w-5 lg:h-5"
                        />
                      </div>
                      <h1 className="text-[12px] lg:text-[14px] font-medium text-slate-500">
                        {pack?.noOfNight}N / {pack?.noOfDays}D
                      </h1>
                    </div>

                    <div className="flex items-center gap-1 lg:gap-2 mt-4 lg:mt-0">
                      <div className="p-1 lg:p-2 border border-neutral-100 shadow-sm rounded-lg">
                        <UserRound
                          size={16}
                          className="text-[#FF5F5F] lg:w-5 lg:h-5"
                        />
                      </div>
                      <span className="flex items-center text-[#6A778B] whitespace-nowrap font-Poppins text-[12px] lg:text-[14px] font-medium leading-[18px] tracking-[0.4px]">
                        {roomCapacityData?.totalAdults} Adults
                        {roomCapacityData?.totalChilds === 0
                          ? ""
                          : roomCapacityData?.totalChilds === 1
                          ? " + 1 Child"
                          : ` + ${roomCapacityData?.totalChilds} Children`}
                      </span>
                    </div>
                  </div>
                </div>

                {/* <div className="lg:grid lg:grid-cols-2 lg:gap-6 mt-4"> */}
                <div className="flex items-center gap-1 lg:gap-2 lg:my-4">
                  <div className="p-1 lg:p-2 border border-neutral-100 shadow-sm rounded-lg">
                    <Calendar
                      size={16}
                      className="text-[#FF5F5F] lg:w-5 lg:h-5"
                    />
                  </div>
                  <h1 className="text-[12px] lg:text-[14px] font-medium text-slate-500">
                    {pack?.fullStartDate} - {pack?.fullEndDate}
                  </h1>
                </div>

                <div className="flex justify-between items-center w-full mt-2 lg:mt-0">
                  <div className="text-[12px] lg:text-[14px] text-slate-500 p-1 font-medium">
                    <h1 className="border border-neutral-200 shadow-sm px-2 py-1 rounded-md">
                      Starts from{" "}
                      <span className="text-[#1EC089]">{pack?.startFrom}</span>
                    </h1>
                  </div>

                  <div className="flex items-end flex-col">
                    <h1 className="text-slate-600 text-lg lg:text-xl font-medium flex items-center">
                      <IndianRupee size={16} className="lg:w-5 lg:h-5" />
                      <span>{formatIndianNumber(pack?.perPerson)}</span>
                    </h1>
                    <h1 className="text-[8px] lg:text-[10px] text-slate-500 mt-[-4px]">
                      per person
                    </h1>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <hr
            className="my-3 px-4"
            style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
          />

          <div className="lg:flex-1 mt-8 lg:mt-0">
            <div className="h-auto border border-neutral-200 w-full bg-[#FFF] rounded-[14px] shadow-sm p-5 lg:p-8">
              <h1 className="font-Poppins text-[17px] lg:text-[22px] text-center font-semibold text-[#FF5F5F] drop-shadow-black drop-shadow-sm mb-6">
                Payment Options
              </h1>
              <div className="px-15 py-5 flex flex-col gap-8 ">
                <section
                  className={`h-auto border w-full py-5 ${
                    option === "advance"
                      ? "border-[#4BCDA1]"
                      : "border-gray-300"
                  } bg-[#FFF] rounded-[14px] mb-8 lg:mb-0`}
                >
                  <label className="flex item-center gap-2 px-4">
                    <input
                      onChange={(e) => handleChangeRadio(e.target.value)}
                      value={"advance"}
                      type="radio"
                      name="select-payment"
                    />
                    <h1 className="text-[#5D6670] font-Poppins flex items-center text-sm font-semibold">
                      Reserve for{" "}
                      <span className="text-xl flex items-center px-1">
                        <IndianRupee size={16} />1
                      </span>
                    </h1>
                  </label>
                  <hr
                    className="my-3 mx-4"
                    style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
                  />
                  <div className="flex items-center py-2  px-4">
                    <span className="bg-[#D0DDEC] rounded-full px-2  py-1 text-xs">
                      1
                    </span>
                    <div className="flex flex-col  pl-4 font-Poppins  py-1 font-semibold">
                      <h1 className="text-[#5D6670] py-1 text-sm ">
                        Book Your Slot
                      </h1>
                      <p className="text-[#1EC089]   text-xs">
                        Pay just 1 and book your travel
                      </p>
                    </div>
                    <span className="text-[#5D6670] ml-auto text-sm pr-5 flex items-center  font-montserrat font-semibold">
                      <IndianRupee className="w-[10px] h-[10px]" />1
                    </span>
                  </div>
                  <div className="flex items-center  px-4">
                    <span className="bg-[#D0DDEC] rounded-full px-2  py-1 text-xs">
                      2
                    </span>
                    <div className="flex flex-col  pl-4 font-Poppins  py-1 font-semibold">
                      <h1 className="text-[#5D6670] py-1 text-sm">
                        15 Days before Tour
                      </h1>
                      <p className="text-[#1EC089]   text-sm">
                        {pack?.fullStartDate}
                      </p>
                    </div>
                    <span className="text-[#5D6670] ml-auto pr-5 text-sm flex items-center  font-montserrat font-semibold">
                      <IndianRupee size={16} />
                      {formatIndianNumber(pack.finalPackagePrice)}
                    </span>
                  </div>
                </section>
              </div>
              <div className="px-15 py-5 flex flex-col gap-8">
                <section
                  className={`h-auto border w-full py-5 ${
                    option === "full" ? "border-[#4BCDA1]" : "border-gray-300"
                  } bg-[#FFF] rounded-[14px] mb-8 lg:mb-0`}
                >
                  <label className="flex item-center gap-2 px-4">
                    <input
                      onChange={(e) => handleChangeRadio(e.target.value)}
                      value={"full"}
                      type="radio"
                      name="select-payment"
                    />
                    <h1 className="text-[#5D6670] font-Poppins flex items-center text-sm font-semibold">
                      Pay full price{" "}
                      <span className="text-xl flex px-1 items-center">
                        <IndianRupee size={16} />{" "}
                        {formatIndianNumber(pack?.finalPackagePrice)}
                      </span>
                    </h1>
                  </label>
                  <hr
                    className="my-3 mx-4"
                    style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
                  />
                  <div className="flex items-center py-2  px-4">
                    <span className="bg-[#D0DDEC] rounded-full px-2  py-1 text-xs">
                      1
                    </span>
                    <div className="flex pl-4 font-Poppins  py-1 font-semibold">
                      <h1 className="text-[#5D6670] py-1 text-sm flex items-center flex-wrap ">
                        Book your travel by paying full amount and&nbsp;{" "}
                        <p className="text-[#1EC089] text-xs ">
                          Get 10X Reward Coins
                        </p>
                      </h1>
                    </div>
                    <span className="text-[#5D6670] ml-auto text-sm pr-5 flex items-center  font-montserrat font-semibold">
                      <IndianRupee className="w-[10px] h-[10px]" />
                      {formatIndianNumber(pack?.finalPackagePrice)}
                    </span>
                  </div>
                </section>
              </div>
              <hr
                className="my-3 px-4"
                style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
              />
              <div>
                {showCoupon && (
                  <section className="flex flex-col gap-2 lg:px-5">
                    <div className="pb-2">
                      <h1 className="text-[#4A5058] py-2 font-Poppins text-[11px] lg:text-[16px] font-medium">
                        Have a coupon code ?
                      </h1>
                      <label
                        className="rounded-lg flex justify-between  items-center  w-full lg:w-[60%] p-2 "
                        style={{ border: " 1px solid rgba(0, 0, 0, 0.23)" }}
                      >
                        <input
                          onInput={(e: any) => setCoupon(e.target.value)}
                          type="text"
                          value={coupon ? coupon : ""}
                          placeholder="Enter Code"
                          className="outline-none text-xs lg:text-base"
                          disabled={disableCouponInput}
                        />
                        {!isCouponApplied && (
                          <button
                            className="text-[9px] lg:ml-auto lg:px-3 pr-2 lg:text-[11px] font-Poppins font-semibold"
                            style={{
                              background:
                                "linear-gradient(90deg, #27B182 -5.26%, #31DAA1 99.73%)",
                              backgroundClip: "text",
                              WebkitBackgroundClip: "text",
                              WebkitTextFillColor: "transparent",
                            }}
                            onClick={() => handleCouponApply(coupon)}
                            disabled={disableCouponInput}
                          >
                            Apply
                          </button>
                        )}

                        {isCouponApplied && (
                          <button
                            className="bg-[#FF5F5F] text-white rounded-md p-1 "
                            onClick={RemoveCoupon}
                          >
                            <X size={10} />
                          </button>
                        )}
                      </label>

                      {showCouponStatus && (
                        <div
                          className=" flex flex-col  border    my-3 drop-shadow-black drop-shadow-sm gap-4 rounded-lg p-4"
                          style={{
                            boxShadow:
                              "2px 4px 20.9px 0px rgba(101, 255, 200, 0.22)",
                            border: "1px solid var(--grd-red, #FF5F5F)",
                          }}
                        >
                          <span className="flex items-center justify-center gap-1">
                            <p className="p-1 rounded-md border-neural-100 shadow-sm border">
                              <TagsIcon
                                size={12}
                                className="align-middle text-[#FF5F5F]"
                              />
                            </p>
                            <h1 className="text-center  font-Poppins text-[17px]  font-medium text-[#FF5F5F] drop-shadow-black drop-shadow-sm">
                              Coupon Details
                            </h1>
                          </span>
                          {/* <div className="">{coupon}</div> */}
                          <div className="flex items-center gap-2">
                            <h1 className="text-[15px] text-neutral-700">
                              Coupon Code:{" "}
                            </h1>

                            <p className="text-neutral-500 text-[14px]">
                              {coupon}
                            </p>
                          </div>
                          <div className="flex  items-center gap-2 ">
                            <p className="text-[16px] text-neutral-700 gap-2">
                              Discount:
                            </p>
                            <span className="text-[15px]  text-neutral-500 flex items-center ">
                              {" "}
                              <IndianRupee size={15} />
                              {formatIndianNumber(couponValue)}
                            </span>
                          </div>
                          <div className="flex  items-center gap-2">
                            <h1 className="text-[15px] text-neutral-700">
                              Total Cost:
                            </h1>
                            <p className="text-[13px] text-neutral-500 flex items-center">
                              <IndianRupee size={15} />{" "}
                              {formatIndianNumber(pack?.finalPackagePrice)}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <h1 className="text-[15px] text-neutral-700">
                              Coupon Applied Price:
                            </h1>
                            <p className="text-[13px] text-neutral-500 flex items-center ">
                              <IndianRupee size={15} />
                              {formatIndianNumber(
                                pack?.finalPackagePrice - couponValue
                              )}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                    {selectedCouponId ? (
                      <div>
                        <CouponSelect
                          selectedCoupon={selectedCouponId}
                          setCoupon={setSelectedCouponId}
                          setShowAllCoupons={setShowAllCoupons}
                          key={selectedCouponId}
                          couponCode={
                            packageCoupons.find(
                              (c) => c.couponId === selectedCouponId
                            )?.code || ""
                          }
                          couponName={
                            packageCoupons.find(
                              (c) => c.couponId === selectedCouponId
                            )?.couponName || ""
                          }
                          couponDescription={
                            packageCoupons.find(
                              (c) => c.couponId === selectedCouponId
                            )?.description || ""
                          }
                          couponId={selectedCouponId}
                          couponValidateDate={
                            packageCoupons.find(
                              (c) => c.couponId === selectedCouponId
                            )?.validDate || ""
                          }
                          couponValue={
                            packageCoupons.find(
                              (c) => c.couponId === selectedCouponId
                            )?.value || 0
                          }
                          couponValueType={
                            packageCoupons.find(
                              (c) => c.couponId === selectedCouponId
                            )?.valueType || ""
                          }
                          applyCoupon={handleCouponApply}
                        />
                      </div>
                    ) : (
                      showCoupon &&
                      packageCoupons && (
                        // packageCoupons.length > 1 &&
                        <div className="flex flex-col py-2 space-y-2 mb-3">
                          <div className="flex justify-between items-center">
                            <h1 className="font-Poppins text-[16px] lg:text-[18px] font-semibold text-[#FF5F5F] drop-shadow-black drop-shadow-sm">
                              Coupons & Offers
                            </h1>

                            <AlertDialog
                              open={showAlert}
                              onOpenChange={setShowAlert}
                            >
                              <AlertDialogTrigger>
                                <button
                                  onClick={showMore}
                                  className="text-slate-500 text-[14px] lg:text-[16px]"
                                >
                                  View More
                                </button>
                              </AlertDialogTrigger>

                              <AlertDialogContent className="h-4/5 overflow-y-auto">
                                <div className="flex flex-col space-y-4">
                                  {packageCoupons.map((c) => (
                                    <CouponSelect
                                      selectedCoupon={selectedCouponId!}
                                      setCoupon={setCoupon}
                                      setShowAllCoupons={setShowAllCoupons}
                                      key={c?._id}
                                      couponCode={c?.code}
                                      couponName={c?.couponName}
                                      couponDescription={c?.description!}
                                      couponId={c?.couponId}
                                      couponValidateDate={c?.validDate}
                                      couponValue={c?.value}
                                      couponValueType={c?.valueType}
                                      applyCoupon={handleCouponApply}
                                      closeAlert={closeAlert}
                                    />
                                  ))}
                                </div>
                                <AlertDialogFooter>
                                  <AlertDialogCancel className="border-[#FF5F5F]">
                                    Close
                                  </AlertDialogCancel>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>

                          {packageCoupons.length > 0 && (
                            <CouponSelect
                              selectedCoupon={selectedCouponId!}
                              setCoupon={setCoupon}
                              key={packageCoupons[0]?._id}
                              couponCode={packageCoupons[0]?.code}
                              couponName={packageCoupons[0]?.couponName}
                              couponDescription={
                                packageCoupons[0]?.description!
                              }
                              couponId={packageCoupons[0]?.couponId}
                              couponValidateDate={packageCoupons[0]?.validDate}
                              couponValue={packageCoupons[0]?.value}
                              couponValueType={packageCoupons[0]?.valueType}
                              applyCoupon={handleCouponApply}
                            />
                          )}
                          {/* <CouponSelect
                      selectedCoupon={selectedCouponId!}
                      setCoupon={setCoupon}
                      key={packageCoupons[0]._id}
                      couponCode={packageCoupons[0].code}
                      couponName={packageCoupons[0].couponName}
                      couponDescription={packageCoupons[0].description!}
                      couponId={packageCoupons[0].couponId}
                      couponValidateDate={packageCoupons[0].validDate}
                      couponValue={packageCoupons[0].value}
                      couponValueType={packageCoupons[0].valueType}
                      applyCoupon={handleCouponApply}
                    /> */}
                        </div>
                      )
                    )}
                    <div>
                      <h1 className="text-[#4A5058] py-2 font-Poppins text-[11px] lg:text-[16px]  font-medium">
                        Redeem Coins
                      </h1>
                      <label
                        className="rounded-lg justify-between  flex items-center w-full lg:w-[60%] p-2"
                        // style={{
                        //   boxShadow:
                        //     "2px 4px 20.9px 0px rgba(101, 255, 200, 0.22)",
                        //   border: "1px solid var(--grd-red, #FF5F5F)",
                        // }}
                        style={{ border: " 1px solid rgba(0, 0, 0, 0.23)" }}
                      >
                        <input
                          type="tel"
                          // onInput={(e: any) => handleRedeemCoins(e.target.valueAsNumber)}
                          onInput={(e: any) =>
                            handleRedeemCoins(e.target.value)
                          }
                          value={redeem}
                          inputMode="numeric"
                          disabled={disableRedeemInput}
                          placeholder="1000 claimable"
                          className="outline-none text-xs lg:text-base"
                        />
                        {!isRedeemApplied && (
                          <button
                            className="text-[9px] lg:text-[11px] lg:px-3 lg:ml-auto pr-2 font-Poppins font-semibold"
                            onClick={() =>
                              handleApply("redeem coins Applied successfully")
                            }
                            style={{
                              background:
                                " linear-gradient(87deg, #FF5F5F -25.84%, #FF5F5F -25.82%, #FF9080 118.31%)",
                              backgroundClip: "text",
                              WebkitBackgroundClip: "text",
                              WebkitTextFillColor: "transparent",
                            }}
                          >
                            Apply
                          </button>
                        )}
                        {isRedeemApplied && (
                          <button
                            onClick={removeRedeem}
                            className="bg-[#FF5F5F] text-white rounded-md p-1 "
                          >
                            <X size={10} />
                          </button>
                        )}
                      </label>
                      <div
                        className=" gap-4 rounded-md  border border-dashed flex flex-col my-3 p-4"
                        style={{
                          boxShadow:
                            "2px 4px 20.9px 0px rgba(101, 255, 200, 0.22)",
                          border: "1px solid var(--grd-red, #FF5F5F)",
                        }}
                      >
                        <span className="flex items-center justify-center gap-1">
                          <p className="p-1 rounded-md border-neural-100 shadow-sm border">
                            <HandCoins
                              size={12}
                              className="align-middle text-[#FF5F5F]"
                            />
                          </p>
                          <h1 className="text-center  font-Poppins text-[17px] lg:text-[18px]  font-medium text-[#FF5F5F] drop-shadow-black drop-shadow-sm">
                            Redeem Coins
                          </h1>
                        </span>
                        <div className="flex items-center gap-2 ">
                          <h1 className="text-[16px] lg:text-[17px] text-neutral-700 gap-2">
                            Available Redeem Coins:
                          </h1>
                          <p className="text-[15px] lg:text-[16px]  text-neutral-500 ">
                            {availableRedeemCoins}
                          </p>
                        </div>
                        <span className="text-[16px] lg:text-[17px] flex items-center  text-neutral-700 gap-2">
                          1 coin =
                          <p className="text-[15px] lg:text-[16px]  text-neutral-500 flex items-center ">
                            <IndianRupee size={15} /> 1
                          </p>
                        </span>
                        {isRedeemApplied && (
                          <span className="flex items-center gap-2">
                            <h1 className="text-[16px] text-neutral-700 gap-2">
                              Applied Redeem Coins:
                            </h1>
                            <p className="text-[15px]  text-neutral-500 ">
                              {redeem}
                            </p>
                          </span>
                        )}
                      </div>
                    </div>
                  </section>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="shadowTop fixed bottom-0 right-0 left-0 z-10 w-full bg-white">
        <div className="max-w-7xl mx-auto px-5 lg:px-8">
          <div className="flex justify-center">
            <hr className="w-[244px] h-[4px] rounded-[22px] bg-bookLineBg text-center" />
          </div>
          <div className="flex justify-between items-center my-5">
            <div className="flex flex-col gap-1">
              {option != "advance" && (
                <Drawer>
                  <DrawerTrigger>
                    <div>
                      <button className="flex items-center font-semibold bg-neutral-100 rounded-lg text-[10px] whitespace-nowrap gap-1 px-2  border text-[#5D6670]">
                        Fare Details
                        <ChevronDown size={14} className="stroke-2" />
                      </button>
                    </div>
                  </DrawerTrigger>

                  <DrawerContent>
                    <DrawerHeader>
                      <DrawerTitle>
                        <FareBreakup
                          adults={roomCapacityData?.totalAdults}
                          child={roomCapacityData?.totalChilds}
                          packagePrice={
                            couponValue > 0
                              ? pack?.finalPackagePrice - couponValue
                              : pack?.finalPackagePrice
                          }
                          gstPer={pack?.gstPer}
                          gstPrice={pack?.gstPrice}
                        />
                      </DrawerTitle>
                    </DrawerHeader>
                    <DrawerFooter>
                      <DrawerClose />
                    </DrawerFooter>
                  </DrawerContent>
                </Drawer>
              )}

              <div className="flex items-center gap-1">
                <h1 className="text-2xl font-semibold flex items-center text-[#5D6670]">
                  <FaIndianRupeeSign size={14} />
                  {/* {price} */}
                  {option === "advance"
                    ? 1
                    : couponValue > 0
                    ? formatIndianNumber(pack?.finalPackagePrice - couponValue)
                    : formatIndianNumber(pack?.finalPackagePrice)}
                </h1>
                <h1 className="text-[#FF5F5F] pt-2 font-medium text-xs whitespace-nowrap">
                  {option === "advance" ? "advance" : "Total Cost"}
                </h1>
              </div>
            </div>
            <button
              onClick={handleBooking}
              className="w-[120px] lg:w-[160px] h-[40px] lg:h-[48px] bg-bookNowBg shadow-bookNowShadow rounded-[30px] text-white text-[14px] lg:text-[16px] font-semibold leading-normal tracking-[0.14px]"
            >
              Pay Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

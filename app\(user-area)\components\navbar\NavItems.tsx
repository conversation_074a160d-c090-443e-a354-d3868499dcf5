import { cn } from "@/lib/utils";
import clsx from "clsx";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";

interface NavItemProps {
  label: string;
  href: string;
  active?: boolean;
  onClick?: () => void;
  light?: boolean; // when true, render white text except keep active item highlighted
}
const NavItems: React.FC<NavItemProps> = ({ label, href, active, onClick, light }) => {
  const router = useRouter();
  const handleClick = (label: string) => {
    if (label === "Log in") {
      router.push("/sign-in");
    } else {
      router.push("/sign-up");
    }
  };
  return (
    <>
      <Link
        href={href}
        className={cn(
          "px-3 py-2 transition-colors",
          light ? (active ? "text-[#FF5F5F]" : "text-white") : "text-neutral-600",
          !light && active && "text-[#FF5F5F] rounded-full ring-inset"
        )}
      >
        {label}
      </Link>
    </>
  );
};

export default NavItems;

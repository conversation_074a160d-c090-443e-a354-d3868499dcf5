"use client";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import React from "react";

interface NavMenuProps {
  label: string;
  icon: LucideIcon;
  href: string;
}

const NavMenu = ({ label, icon: Icon, href }: NavMenuProps) => {
  const pathName = usePathname();
  const router = useRouter();
  const isActive = (pathName === "/" && href === "/") || pathName === href;

  return (
    <Link href={href}>
      <div
        className={cn(
          "flex flex-col gap-1 items-center text-slate-500",
          isActive && "text-[#FF5F5F]"
        )}
      >
        <Icon className="" style={{ color: 'rgba(255, 255, 255, 1)' }} />
        <h1 className="text-xs">{label}</h1>
      </div>
    </Link>
  );
};

export default NavMenu;

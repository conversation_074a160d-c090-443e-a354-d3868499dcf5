import React from "react";
import { Clock, User, Hotel, MapPin } from "lucide-react";
import { PiFlagPennantLight } from "react-icons/pi";
import { PackDestination } from "@/app/types/pack";

interface PropsType {
  plan: string;
  destinations: PackDestination[];
  noOfDays: number;
  noOfNights: number;
  totalAdult: number;
  totalChild: number;
  hotelCount: number;
  startsFrom: string;
}

const PackageHighlight = ({
  plan,
  destinations,
  noOfDays,
  noOfNights,
  totalAdult,
  totalChild,
  hotelCount,
  startsFrom,
}: PropsType) => {
  return (
    <div className=" relative pt-6 sm:flex  sm:flex-col">
      <h1
        className={`shadow-pkgShadow bg-${plan?.toLowerCase()}Gradient text-white text-[10px] not-italic font-Poppins font-semibold tracking-[0.3px] leading-normal w-[75px] lg:w-[150px] lg:ml-5 lg:text-xl flex items-center justify-center p-[2px] h-[25px] lg:h-[42px] rounded-[8px] rounded-tr-[2px] rounded-bl-[2px] mt-[16px]`}
      >
        {plan}
      </h1>
      <h1
        className="mt-10 lg:px-5 font-Poppins text-left text-[18px] lg:text-[24px] not-italic font-semibold leading-normal tracking-[0.18px] bg-clip-text text-transparent"
        style={{
          textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.11)",
          backgroundImage:
            "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
        }}
      >
        Package Highlights
      </h1>
      <div className="lg:hidden">
        <div className="ml-[4px] mt-[19px] lg:flex lg:items-center justify-center">
          <div className="flex">
            <span>
              <MapPin
                size={35}
                className=" p-[6px] border border-neutral-200  rounded-[8px] shadow-sm stroke-[1.5px] stroke-[#FF7865]"
                // style={{ background: "rgba(255, 120, 101, 0.05)" }}
              />
            </span>
            <span className="flex w-full overflow-scroll">
              &nbsp;&nbsp;
              {destinations?.map(
                (dest, i) =>
                  dest?.noOfNight > 0 && (
                    <div
                      className="flex items-center justify-between text-nowrap"
                      key={i}
                    >
                      <div className="flex items-center text-[#FF7865] font-Poppins text-[14px] not-italic font-semibold">
                        &nbsp;{dest?.noOfNight}N
                      </div>
                      <div className="flex items-center text-[#6A778B] font-Poppins text-[14px] not-italic font-semibold">
                        &nbsp;-&nbsp;{dest?.destinationName}
                      </div>
                      {i != destinations?.length - 1 && (
                        <span className="text-[#6A778B]"> &nbsp;| &nbsp;</span>
                      )}
                    </div>
                  )
              )}
            </span>
          </div>
          <div className="flex mt-[23px]">
            <span>
              <Clock
                size={35}
                className=" border border-neutral-200 p-[6px] shadow-sm rounded-[8px] stroke-[1.5px] stroke-[#FF7865]"
                // style={{ background: "rgba(255, 120, 101, 0.05)" }}
              />
            </span>
            <p className="flex items-center pl-[9px] text-[#6A778B] font-Poppins text-[14px] not-italic font-medium leading-[18px] tracking-[0.4px]">
              {noOfNights}N/{noOfDays}D
            </p>
            <span className="pl-[44px]">
              <User
                size={35}
                className="border border-neutral-200 shadow-sm p-[6px] rounded-[8px] stroke-[1.5px] stroke-[#FF7865]"
                // style={{ background: "rgba(255, 120, 101, 0.05)" }}
              />
            </span>
            <span className="flex items-center pl-[10px] text-[#6A778B] whitespace-nowrap font-Poppins text-[14px] not-italic font-medium leading-[18px] tracking-[0.4px]">
              {totalAdult} Adults{" "}
              {totalChild === 0
                ? ""
                : totalChild === 1
                ? "+ 1 Child"
                : `+ ${totalChild} Children`}
            </span>
          </div>
          <div className="flex mt-[23px]">
            <span>
              <Hotel
                size={35}
                className="shadow-sm  border border-neutral-200 p-[6px] rounded-[8px] stroke-[1.5px] stroke-[#FF7865]"
                // style={{ background: "rgba(255, 120, 101, 0.05)" }}
              />
            </span>
            <p className="flex items-center pl-[9px] text-[#6A778B] font-Poppins text-[14px] not-italic font-medium leading-[18px] tracking-[0.4px]">
              {hotelCount} Hotels
            </p>
            <span className="pl-9">
              <PiFlagPennantLight
                size={35}
                className=" shadow-sm border border-neutral-200 p-[6px] rounded-[8px] stroke-[1.5px] text-[#FF7865]"
                // style={{ background: "rgba(255, 120, 101, 0.05)" }}
              />
            </span>
            <div className="flex flex-col justify-center gap-0 pl-[10px]">
              <h1 className="text-[#6A778B] font-medium text-[12px]">
                Starts from
              </h1>
              <h1 className="text-[14px] text-[#3bc595] font-semibold">
                {startsFrom}
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop View */}
      <div className="hidden lg:block lg:mt-[45px] lg:px-5">
        {/* First row */}
        <div className="flex items-center gap-16 mb-8">
          <div className="flex space-x-4 items-center">
            <MapPin className="text-[#FF7865] h-5 w-5 shrink-0" />
            <div className="flex flex-wrap gap-2">
              {destinations
                ?.filter((dest) => dest?.noOfNight > 0)
                .map((dest, i, arr) => (
                  <div
                    key={dest?.destinationName || i}
                    className="flex gap-2 items-center text-[14px] lg:text-[16px] whitespace-nowrap"
                  >
                    <span className="text-[#FF7865] font-semibold">
                      {dest?.noOfNight}N
                    </span>
                    <span className="text-[#6A778B] font-semibold">
                      &nbsp;-&nbsp;{dest?.destinationName}
                    </span>
                    {i !== arr.length - 1 && (
                      <span className="text-[#6A778B] mx-2 last:hidden">|</span>
                    )}
                  </div>
                ))}
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Clock
              size={40}
              className="border border-neutral-200 p-[6px] shadow-sm rounded-[8px] stroke-[1.5px] stroke-[#FF7865]"
            />
            <span className="text-[14px] lg:text-[16px] text-[#6A778B] font-medium">
              {noOfNights}N/{noOfDays}D
            </span>
          </div>

          <div className="flex items-center gap-3">
            <User
              size={40}
              className="border border-neutral-200 p-[6px] shadow-sm rounded-[8px] stroke-[1.5px] stroke-[#FF7865]"
            />
            <span className="text-[14px] lg:text-[16px] text-[#6A778B] font-medium whitespace-nowrap">
              {totalAdult} Adults{" "}
              {totalChild === 0
                ? ""
                : totalChild === 1
                ? "+ 1 Child"
                : `+ ${totalChild} Children`}
            </span>
          </div>
        </div>

        {/* Second row */}
        <div className="flex items-center gap-16">
          <div className="flex items-center gap-3">
            <Hotel
              size={40}
              className="border border-neutral-200 p-[6px] shadow-sm rounded-[8px] stroke-[1.5px] stroke-[#FF7865]"
            />
            <span className="text-[14px] lg:text-[16px] text-[#6A778B] font-medium">
              {hotelCount} Hotels
            </span>
          </div>

          <div className="flex items-center gap-3">
            <PiFlagPennantLight
              size={40}
              className="border border-neutral-200 p-[6px] shadow-sm rounded-[8px] stroke-[1.5px] text-[#FF7865]"
            />
            <div>
              <p className="text-[14px] lg:text-[16px] text-[#6A778B] font-medium">
                Starts from
              </p>
              <p className="text-[16px] lg:text-[18px] text-[#3bc595] font-semibold">
                {startsFrom}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PackageHighlight;

import type { Config } from "tailwindcss"

const config = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    './src/**/*.{js,tsx}',
	],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      screens : {
        sm : '320px'
      },
      colors: {
        "app-primary" : "#FF7865",
        "app-secondary" : "#1EC089",
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
       
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
        '8/2':'8px 2px'
      },
      textColor: {
        'green-gradient': "linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)"
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      boxShadow:{
        "pkgShadow" : "4px 8px 25.8px 0px rgba(0, 0, 0, 0.06)",
        "pkg-imgShadow":"2px 4px 13.2px 0px rgba(0, 0, 0, 0.06)",
        "goldShadow":"7px 3px 13px 0px rgba(0, 0, 0, 0.22);",
        "bookingOverviewShadow":"4px 8px 39.7px 0px rgba(255, 120, 101, 0.15);",
        "iternaryStoryShadow":"0px 6px 21.1px 0px rgba(51, 214, 159, 0.15)",
          "itenaryStoryView":"4px 4px 17.1px 0px rgba(101, 255, 190, 0.25)",
          "bookNowShadow":"2px 4px 20.9px 0px rgba(255, 120, 101, 0.22)",
        "changeHotelBoxShadow":"4px 8px 25.8px 0px rgba(51, 214, 159, 0.18)",
        "changeHotelImgShadow":"2px 4px 13.8px 0px rgba(0, 0, 0, 0.11)",
        "changeHotelSelectedShadow":"2px 4px 15.1px 0px rgba(101, 255, 181, 0.49)",
        "changeHotelSelectShadow":"2px 4px 15.1px 0px rgba(101, 255, 181, 0.49)",
        "changeCabSelectedContShadow":"4px 8px 25.8px 0px rgba(51, 214, 159, 0.11)",
        "changeCabSelectContShadow":"4px 8px 26.3px 0px rgba(172, 161, 159, 0.18)",
        "myBookingsGoldContShadow":"4px 8px 25.8px 0px rgba(255, 214, 72, 0.15)",
        "myBookingsSilverContShadow":"4px 8px 25.8px 0px rgba(0, 0, 0, 0.06)",
        "addActivityContShadow":"4px 8px 25.8px 0px rgba(172, 161, 159, 0.34)",
        "addActivityImgContShadow":"2px 4px 13.8px 0px rgba(0, 0, 0, 0.11)",
        "addActivityPrivateActivityShadow":"2px 4px 14.3px 0px rgba(255, 120, 101, 0.20)",
        "myIternaryContShadow":"4px 4px 17.9px 0px rgba(255, 120, 101, 0.09)",
        "myIternaryViewButtonShadow":"2px 4px 14.3px 0px rgba(255, 120, 101, 0.20)"
    },
    borderColor:{
      'itenaryStorycont':'rgba(172, 161, 159, 0.12)',
      'changeHotelLine':'rgba(0, 0, 0, 0.08)',
      'changeCabLine':"rgba(0, 0, 0, 0.08)",
      
      'myBookingsLine':"rgba(0, 0, 0, 0.08)",
      'addActivityContBorder':"rgba(172, 161, 159, 0.34)"
    },
    backgroundColor:{
      'itenaryStorybg':"rgba(255, 255, 255, 0.05);",
      "changeHotelImgBg":"linear-gradient(278deg, rgba(0, 0, 0, 0.49) 9.45%, rgba(102, 102, 102, 0.06) 55.44%, rgba(0, 0, 0, 0.44) 89.04%)",
    },
    fontFamily:{
      'inter':['Inter'],
      'montserrat':['Montserrat'],
      'Poppins': ['Poppins', 'sans-serif'],
      
    },
    backgroundImage:{
      'goldGradient': 'linear-gradient(75deg, #EF831E -0.94%, rgba(255, 215, 72, 1.00) 49.26%, rgba(246, 129, 21, 0.94) 109.25%)',
      'silverGradient':'linear-gradient(75deg, #727B85 -0.94%, rgba(190, 198, 207, 1.00) 49.26%, rgba(73, 80, 88, 0.94) 109.25%)',
	    'platinumGradient':'linear-gradient(75deg, #CA0B0B -0.94%, rgba(255, 77, 87, 1.00) 49.26%, rgba(219, 13, 13, 0.94) 109.25%)',
      'redGradient':"linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%))",
      "bookNowBg": "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
      "bookLineBg":"linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
      "changeHotelSelected":"linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)",
      "myBookingsBookingDoneBg":"linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)",
      "myBookingsBookingFailedBg":"linear-gradient(90deg, #FF9080 -5.26%, #FF5F5F 99.73%)",
      "myBookingsWaitingBg":"linear-gradient(91deg, #FFA236 -7.11%, #F68115 99.47%)",
      "myBookingsPendingBg":"linear-gradient(91deg, #AFA236 -7.11%, #A68115 99.47%)",
      "addActivityImgContBg":"linear-gradient(278deg, rgba(0, 0, 0, 0.49) 9.45%, rgba(102, 102, 102, 0.06) 55.44% , rgba(0, 0, 0, 0.44) 89.04%)",
      "myIternaryContBg":"linear-gradient(0deg, #FFF 0%, #FFF 100%), rgba(255, 120, 101, 0.00)",
      "myIternaryImgContBg":"linear-gradient(278deg, rgba(0, 0, 0, 0.49) 9.45%, rgba(102, 102, 102, 0.06) 55.44%, rgba(0, 0, 0, 0.44) 89.04%)",
      "myIternaryViewButtonBg":"linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)"

    },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config

export default config
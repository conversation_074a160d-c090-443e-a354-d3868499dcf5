import { RootState } from "@/app/store/store";

/**
 * Constructs URL query parameters from Redux state for package navigation
 * @param state - Redux root state
 * @returns URLSearchParams object with filter parameters
 */
export const buildPackageUrlParams = (state: RootState): URLSearchParams => {
  const params = new URLSearchParams();

  // Add destination parameters
  const searchPackage = state.searchPackage;
  if (searchPackage.destination) {
    params.set('fromCity', encodeURIComponent(searchPackage.destination));
  }
  if (searchPackage.date) {
    // Format date as YYYY-MM-DD for depDate parameter
    const date = new Date(searchPackage.date);
    if (!isNaN(date.getTime())) {
      params.set('depDate', date.toISOString().split('T')[0]);
    }
  }

  // Add theme/interest parameters
  const themeSelect = state.themeSelect;
  if (themeSelect.theme) {
    params.set('pkgType', encodeURIComponent(themeSelect.theme));
  }

  // Add room capacity parameters
  const roomSelect = state.roomSelect;
  if (roomSelect.room.totalRooms > 0) {
    params.set('rooms', roomSelect.room.totalRooms.toString());
  }
  if (roomSelect.room.totalAdults > 0) {
    params.set('adults', roomSelect.room.totalAdults.toString());
  }
  if (roomSelect.room.totalChilds > 0) {
    params.set('children', roomSelect.room.totalChilds.toString());
  }
  if (roomSelect.room.perRoom > 0) {
    params.set('perRoom', roomSelect.room.perRoom.toString());
  }

  // Add package category if available
  const pkgCategory = state.pkgCategory;
  if (pkgCategory.pkgCategory) {
    params.set('category', encodeURIComponent(pkgCategory.pkgCategory));
  }


  return params;
};

/**
 * Constructs a package detail URL with preserved filter parameters
 * @param packageId - The package ID
 * @param state - Redux root state
 * @returns Complete URL with query parameters
 */
export const buildPackageDetailUrl = (packageId: string, state: RootState): string => {
  const params = buildPackageUrlParams(state);
  const queryString = params.toString();
  
  if (queryString) {
    return `/package/${packageId}?${queryString}`;
  }
  
  return `/package/${packageId}`;
};

/**
 * Parses URL search parameters and returns them as an object
 * @param searchParams - URLSearchParams or search string
 * @returns Object with parsed parameters
 */
export const parseUrlParams = (searchParams: URLSearchParams | string) => {
  const params = typeof searchParams === 'string' 
    ? new URLSearchParams(searchParams) 
    : searchParams;

  const result: Record<string, string | string[]> = {};

  params.forEach((value, key) => {
    if (key === 'selectedFilters') {
      result[key] = value.split(',').filter(Boolean);
    } else {
      result[key] = decodeURIComponent(value);
    }
  });

  return result;
};

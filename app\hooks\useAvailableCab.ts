import { useCallback, useEffect, useState } from "react";
import { NEXT_PUBLIC_API_URL } from "../utils/constants/apiUrls";
import axios from "axios";

export const useAvailableCab = (packageId: string, destinationId: string) => {
  const [cab, setCab] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [err, setErr] = useState("");

  //package/PLMANHO-87JX-6D5N2A/available/get
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const resp = await axios.get(
        NEXT_PUBLIC_API_URL + `package/${packageId}/vehicle/get`
      );
      console.log(resp, "response form ");
      const filteredHotels = resp?.data?.result[0].vehicleDetails;
      console.log(destinationId, "des");
      console.log(filteredHotels, "filtered cab");

      setCab(filteredHotels);
    } catch (err: any) {
      setErr(err.message);
    } finally {
      setLoading(false);
    }
  }, []);
  useEffect(() => {
    fetchData();
  }, []);

  return { cab, isLoading, err };
};

"use client";
import React from "react";
import SearchPackage from "./components/home/<USER>/SearchPackage";
import ExplorePackages from "./components/home/<USER>/ExplorePackages";
import Accordion from "./components/home/<USER>";
import DestinationGallery from "./components/home/<USER>";
import Footer from "./components/home/<USER>";
import Link from "next/link";
import { FaWhatsapp } from "react-icons/fa6";
import Image from "next/image";

const faqItems = [
  {
    question: "What is Tripxplo?",
    answer:
      "TripXplo, registered as Tripmilestone Tours Pvt. Ltd, was founded in 2020 by a team of young travel enthusiasts. A Travel Company for travellers that have big destinations in mind and huge milestones to traverse yet low on pocket.",
  },
  {
    question:
      "How are TripXplo packages different from other online travel sites?",
    answer:
      "Our packages offer a comprehensive range of services including travel, sightseeing, breakfast, adventures, and accommodation. Additionally, we have a network of well-trained Trip Captains across India who guide our customers, ensuring they feel at home and can travel with ease.",
  },
  {
    question:
      "What are the different modes of payment for booking a trip on TripXplo?",
    answer:
      "We provide various payment options for a seamless booking experience:",
    listItems: ["Debit/Credit Cards", "UPI", "Net Banking", "NEFT Transfer"],
  },
  {
    question: "What is included in the tour package?",
    answer:
      "The inclusions and exclusions of a tour package may vary depending on the specific package you choose. Generally, our tour packages include accommodation, transportation, sightseeing, and meals (as specified in the itinerary). Additional activities or services may be available at an extra cost.",
  },

  {
    question: "Is the price of the tour package per person or per group?",
    answer:
      "The price of the tour package is generally per person, based on double occupancy. Single occupancy and group rates are also available, with pricing varying depending on the package you choose.",
  },

  {
    question: "What is TripXplo's cancellation policy?",
    answer:
      "Our cancellation policy varies depending on the type of tour package and the booking date. Generally:",
    listItems: [
      "Cancellations made more than 30 days before the tour date are eligible for a full refund.",
      "Cancellations made between 15-30 days before the tour date are eligible for a 50% refund.",
      "Cancellations made within 15 days before the tour date are not eligible for a refund.",
    ],
  },

  {
    question: "What is your refund policy?",
    answer:
      "All refunds are processed within 21 days from the day of cancellation of the trip.",
  },

  {
    question: "Can I make changes to the tour itinerary after booking?",
    answer:
      "We understand that plans may change and we will try our best to accommodate any changes to the itinerary. However, please note that any changes made after the booking may incur additional charges.",
  },

  {
    question: "Is travel insurance included in the tour package?",
    answer:
      "No, travel insurance is not included in the tour package. We recommend purchasing travel insurance separately to cover any unexpected events or accidents during your trip.",
  },

  {
    question: "Can I request a vegetarian/vegan/gluten-free meal?",
    answer:
      "Yes, you can request a specific type of meal during the booking process or inform us in advance. We will do our best to accommodate your dietary preferences.",
  },
];
const items = [
  {
    src: "/bali.jpg",
    alt: "Bali",
    text: "Bali",
    destinationId: "4ebe5f1e-99d4-4dbb-a4e5-538a353ba81c",
  },
  {
    src: "/goa.jpg",
    alt: "Goa",
    text: "Goa",
    destinationId: "1961511a-2d52-4dc4-95f5-9478c3e9a04f",
  },
  {
    src: "/manali.jpg",
    alt: "Manali",
    text: "Manali",
    destinationId: "9380c50d-62ee-443b-a5c9-6beb90770e8f",
  },
  {
    src: "/ooty.jpg",
    alt: "Ooty",
    text: "Ooty",
    destinationId: "f9b8a4f8-227a-4464-a650-c30b8ec7f914",
  },
  {
    src: "/meghalaya.jpg",
    alt: "Meghalaya",
    text: "Meghalaya",
    destinationId: "e431c796-3946-4d73-a9b9-99a7b138680d",
  },
  {
    src: "/kashmir.jpg",
    alt: "Kashmir",
    text: "Kashmir",
    destinationId: "009b592f-9b73-4990-8068-1c299d1f15e5",
  },
];

const App = () => {
  const contactNumber = process.env.NEXT_PUBLIC_CONTACT_NUM;

  return (
    <>
      {/* Make hero full-width by placing it outside the padded container */}
      <SearchPackage />

      <div className="px-4 sm:px-6 md:px-8 lg:px-24 lg:pt-10 sm:pt-0">
        {/* <ExplorePackages /> */}

        <div className="mt-8 lg:mt-[84px] flex flex-col bg-white">
          <h1 className="font-bold text-2xl sm:text-3xl lg:text-4xl text-[#1EC089] mb-3 tracking-tight">
            Top Destinations
          </h1>
          <h1 className="text-neutral-600 text-base sm:text-lg lg:text-xl font-medium mb-4">
            Our Popular Packages Destinations
          </h1>
          <DestinationGallery items={items} />
        </div>

        <div className="mt-12 sm:mt-20 lg:mt-32">
          <div
            id="faq"
            className="text-center mb-12 w-full"
          >
            <span className="lg:hidden text-2xl font-bold text-[#FF5F5F] tracking-tight">FAQ&apos;s</span>
            <span className="hidden font-bold lg:text-4xl lg:block text-[#FF5F5F] tracking-tight">
              Frequently Asked Questions
            </span>
            <p className="text-neutral-600 text-base sm:text-lg mt-3 font-medium">
              Find answers to common questions about our travel packages
            </p>
          </div>

          {/* Content Container - Centered */}
          <div className="max-w-4xl mx-auto">
            {/* Added wrapper for centering */}
            <Accordion items={faqItems} />
          </div>
        </div>

        <div className="fixed bottom-20 right-4 sm:bottom-28 sm:right-10 flex items-center justify-center">
          <div className="wave absolute w-14 h-14 rounded-full  z-0 transform "></div>

          <div className="fixed cursor-pointer  rounded-full w-16 h-16 flex items-center  bg-green-400 justify-center  shadow-lg">
            <Link
              href={`https://wa.me/917695993808?text=Hello%2C%20I%27m%20interested%20in%20TripXplo%20Travel%20package`}
              target="_blank"
            >
              <FaWhatsapp className="fill-white w-10 h-10" />
            </Link>
          </div>
        </div>

        <div className="mt-6 mb-20">
          <Footer />
        </div>
      </div>
    </>
  );
};

export default App;
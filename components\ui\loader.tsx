import React from 'react';

export default function Loader() {
  return (
    <div className='flex items-center justify-center h-screen'>
      {/* Mobile view */}
      <div className='flex justify-center items-center space-x-2'>
        <div className="w-[20px] h-[20px] border-4 border-[#FF7865] rounded-full animate-[bounce_1.5s_ease-in-out_infinite]"></div>
        <div className="w-[30px] h-[30px] border-4 border-[#FF7865] rounded-full animate-[bounce_1.5s_ease-in-out_infinite_0.2s]"></div>
        <div className="w-[20px] h-[20px] border-4 border-[#FF7865] rounded-full animate-[bounce_1.5s_ease-in-out_infinite_0.4s]"></div>
        <div className="w-[30px] h-[30px] border-4 border-[#FF7865] rounded-full animate-[bounce_1.5s_ease-in-out_infinite_0.6s]"></div>
        <div className="w-[20px] h-[20px] border-4 border-[#FF7865] rounded-full animate-[bounce_1.5s_ease-in-out_infinite_0.8s]"></div>
      </div>
    </div>
  );
}

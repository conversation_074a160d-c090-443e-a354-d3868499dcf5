import { BookingPayloadType } from '@/app/types/BookingPayload';
import api from './auth';
import toast from 'react-hot-toast';
export  const createBooking = async (body :BookingPayloadType) => {
    try {
        const response = await api.post(`package/booking`, body);
        return Promise.resolve(response);
      } catch (error:any) {
        console.log(error);
        return Promise.reject(error);
      }
}
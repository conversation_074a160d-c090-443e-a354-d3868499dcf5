// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import {getAuth,GoogleAuthProvider} from "firebase/auth"
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyBfGUGIqNC6G3HHr6VKuCJmrjG95MLnjig",
  authDomain: "tripxplo-b683b.firebaseapp.com",
  projectId: "tripxplo-b683b",
  storageBucket: "tripxplo-b683b.appspot.com",
  messagingSenderId: "426422161258",
  appId: "1:426422161258:web:672ddeddc6c691348e5f13",
  measurementId: "G-5MBHTWVPEF"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app)
const provider = new GoogleAuthProvider();
export {auth , provider};
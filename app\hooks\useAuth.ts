import { useQuery } from 'react-query';
import { getProfile } from '../utils/api/getProfile';

export function useAuth() {
  const { data: userInfo, status: queryStatus } = useQuery(
    ['user-profile'],
    getProfile,
    {
      staleTime: 0,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
    }
  );

  const user = userInfo?.result;
  const isAuthenticated = !!user;
  const isLoading = queryStatus === 'loading';
  const isError = queryStatus === 'error';

  return { user, isAuthenticated, isLoading, isError };
}

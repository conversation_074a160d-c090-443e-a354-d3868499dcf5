"use client";
import React, { useEffect } from "react";
import { Parentheses, User } from "lucide-react";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Image from "next/image";
import { useQuery } from "react-query";
import { getInterest } from "@/app/actions/get-interest";
import { cn } from "@/lib/utils";
import { selectTheme, selectThemeId } from "@/app/store/features/selectThemeSlice";
import { RootState } from "@/app/store/store";
import { selectPerRooom } from "@/app/store/features/roomCapacitySlice";

export interface Interest {
  image: string;
  interestId: string;
  interestName: string;
  isFirst: boolean;
  sort: number;
  _id: string;
}
interface PackageThemeProps {
  scrollRef: React.RefObject<HTMLDivElement>;
}

const PackageThemes = ({ scrollRef }: PackageThemeProps) => {
  const dispatch = useDispatch();


  const themeSelected = useSelector(
    (state: RootState) => state.themeSelect.theme
  );
  const [focused, setFocused] = useState(themeSelected);
  const { data: themes, isLoading } = useQuery<Interest[]>(
    "fetch Interest",
    getInterest
  );
  console.log(themes)

  const handleFocus = (pack: any) => {
    setFocused(pack.interestName);

    dispatch(selectTheme({ selectedTheme: pack.interestName }));
    dispatch(selectThemeId({ selectedThemeId: pack.interestId }));
    if(pack.interestName === "Honeymoon" || pack.interestName === "Couple"){
      dispatch(selectPerRooom(pack.perRoom));
    }
  };
  
  async function fetchData() {
    const data = await getInterest();
    const filtered = data.find((k:Interest)=>k.interestName === themeSelected);
    console.log('Filtered',filtered);

    dispatch(selectThemeId({ selectedThemeId: filtered.interestId }));
   }
  useEffect(() => {  
  fetchData()
  }, []);

  return (
    <>
      <div className="flex items-center space-x-4 overflow-x-auto px-2 pt-4 ">
        {isLoading ? (
          <>
            <div className="w-28 animate-pulse h-10 bg-slate-200 rounded-lg flex-shrink-0"></div>
            <div className="w-28 animate-pulse h-10 bg-slate-200 rounded-lg flex-shrink-0"></div>
            <div className="w-28 animate-pulse h-10 bg-slate-200 rounded-lg flex-shrink-0"></div>
            <div className="w-28 animate-pulse h-10 bg-slate-200 rounded-lg flex-shrink-0"></div>
          </>
        ) : (
          themes?.map((theme) => (
            <div
              onClick={() => handleFocus(theme)}
              key={theme._id}
              className={cn(
                "px-3 border flex  rounded-lg flex-shrink-0 border-[#FF7865] items-center gap-2 py-2 cursor-pointer ",
                focused === theme.interestName && "bg-[#FF7865] text-white"
              )}
            >
              <div className="relative h-5 w-5">
                {theme.image !== undefined && focused === theme.interestName ?(
                    <Image
                    loading="eager"
                    fill
                    className={focused ?"  h-2 w-2 object-cover ":" h-2 w-2 object-cover"}
                    alt={theme.interestName}
                    src={`https://tripemilestone.in-maa-1.linodeobjects.com/interest/${theme.interestName+'-white.svg'}`}
                  />
                
                ) : (
                  <Image
                  loading="eager"
                  fill
                  className={focused ?"  h-2 w-2 object-cover bg-white":" h-2 w-2 object-cover"}
                  alt={theme.interestName}
                  src={`https://tripemilestone.in-maa-1.linodeobjects.com/${theme.image}`}
                />
                )
              }
              </div>
              <h1 className="sm:text-[12px] lg:text-[14px]">{theme.interestName}</h1>
            </div>
          ))
        )}
      </div>
    </>
  );
};

export default PackageThemes;
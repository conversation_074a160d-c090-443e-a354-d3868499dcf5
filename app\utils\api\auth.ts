import axios from 'axios';
import toast from 'react-hot-toast';
import {
  cleanToken,
  getAccessToken,
  getRefreshToken,
  setAccessToken,
  setRefreshToken,
} from '../constants/accessToken';
// import { history } from '@/app/(auth)/sign-in/page';
import { NEXT_PUBLIC_API_URL } from '../constants/apiUrls';
import Router from 'next/router';

const instance = axios.create({
  baseURL: NEXT_PUBLIC_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

instance.interceptors.request.use(
  (config) => {
    const headerConfig = config;

    headerConfig.headers.Authorization = `bearer ${getAccessToken()}`;

    return headerConfig;
  },
  (error) => {
    return Promise.reject(error);
  }
);

instance.interceptors.response.use(
  (res) => {
    return res;
  },
  async (err) => {
    const originalConfig = err.config;
    console.log('oc', originalConfig);
    if (originalConfig.url !== 'auth/login' && err.response) {
      // Access Token was expired
      if (err.response.status === 401 && !originalConfig._retry) {
        originalConfig._retry = true;
        try {
          const refreshToken = getRefreshToken();

          if (refreshToken) {
            await instance
              .put('auth/refreshToken', {
                refreshToken,
              })
              .then((response) => {
                setRefreshToken(response?.data?.result?.refreshToken);

                setAccessToken(response?.data?.result?.accessToken);

                instance.defaults.headers.common.Authorization = `bearer ${response?.data?.result?.accessToken}`;
              })
              .catch((error) => {
                console.log(error);
                cleanToken();

                toast.error('Refresh token is expired');
                Router.push('/sign-in');
              });
          }

          return instance(originalConfig);
        } catch (_error: any) {
          if (_error.response && _error.response.data) {
            return Promise.reject(_error.response.data);
          }

          return Promise.reject(_error);
        }
      }
    }
    return Promise.reject(err);
  }
);

export default instance;

import { initialLoad } from "@/app/store/features/roomCapacitySlice";
import {
  changeDate,
  changeDestination,
  changeDestinationId,
} from "@/app/store/features/searchPackageSlice";
import { MapPin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import test from "node:test";
import React from "react";
import { useDispatch } from "react-redux";

interface GalleryItemProps {
  src: string;
  alt: string;
  text: string;
  featured?: boolean;
  destinationId: string;
}

const GalleryItemMobile: React.FC<GalleryItemProps> = ({
  src,
  alt,
  text,
  destinationId,
}) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const handleClick = (text: string, destinationId: string) => {
    dispatch(changeDestination(text));
    dispatch(changeDestinationId(destinationId));
    // Calculate date 10 days from now
    const newDate = new Date();
    newDate.setDate(newDate.getDate() + 10);

    // Convert to local date
    const localDate = new Date(
      newDate.getTime() - newDate.getTimezoneOffset() * 60000
    );

    // Dispatch the date
    dispatch(changeDate(localDate.toISOString()));
    dispatch(initialLoad());
    router.push(`/packages`);
  };
  return (
    <div
      className="sm:relative lg:hidden overflow-hidden rounded-2xl bg-black group cursor-pointer shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
      onClick={() => handleClick(text, destinationId)}
    >
      <Image
        src={src}
        alt={alt}
        width={100}
        height={100}
        layout="responsive"
        objectFit="cover"
        className="transition-transform duration-500 ease-in-out transform group-hover:scale-110"
      />
      <div className="absolute inset-0 flex flex-col justify-between p-4 bg-gradient-to-t from-black/80 via-black/20 to-transparent">
        <div className="flex-1 flex items-center justify-center">
          <div className="px-4 py-2 bg-gradient-to-r from-white to-gray-50 text-center text-[#FF5F5F] text-sm rounded-xl font-normal opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 shadow-lg">
            Explore Packages
          </div>
        </div>
        <p className="flex font-bold items-center gap-2 text-white text-sm [text-shadow:_2px_2px_8px_rgb(0_0_0_/_80%)] tracking-wide">
          <MapPin size={20} className="text-orange-300" />
          {text}
        </p>
      </div>
    </div>
  );
};

const GalleryItem: React.FC<GalleryItemProps> = ({
  src,
  alt,
  text,
  featured,
  destinationId,
}) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const handleClick = (text: string, destinationId: string) => {
    dispatch(changeDestination(text));
    dispatch(changeDestinationId(destinationId));
    // Calculate date 10 days from now
    const newDate = new Date();
    newDate.setDate(newDate.getDate() + 10);

    // Convert to local date
    const localDate = new Date(
      newDate.getTime() - newDate.getTimezoneOffset() * 60000
    );

    // Dispatch the date
    dispatch(changeDate(localDate.toISOString()));
    dispatch(initialLoad());
    router.push(`/packages`);
  };
  return (
    <div
      className={`relative overflow-hidden rounded-2xl bg-black group shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 ${
        featured ? "h-full" : "h-64"
      }`}
    >
      <Image
        src={src}
        alt={alt}
        fill
        className="object-cover transition-transform duration-500 ease-in-out transform group-hover:scale-110"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
      <h1 className="text-xl lg:text-2xl font-bold flex items-center gap-3 text-white absolute bottom-6 left-6 [text-shadow:_2px_2px_8px_rgb(0_0_0_/_80%)] tracking-wide">
        <MapPin size={22} className="text-orange-300" /> {text}
      </h1>
      <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-black/80 via-black/60 to-black/80 text-center text-lg font-semibold opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100">
        <div
          onClick={() => handleClick(text, destinationId)}
          className="px-6 py-3 bg-gradient-to-r from-white to-gray-50 text-[#FF5F5F] rounded-xl font-normal cursor-pointer shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-200"
        >
          Explore Packages
        </div>
      </div>
    </div>
  );
};

interface GalleryProps {
  items: GalleryItemProps[];
}

const DestinationGallery: React.FC<GalleryProps> = ({ items }) => {
  console.log(items, "items");
  // Mobile and tablet view
  const mobileView = (
    <div className="mt-6 w-full lg:hidden block max-w-5xl p-6 pb-12 mx-auto mb-12 gap-6 sm:columns-2 space-y-6">
      {items.map((item, index) => (
        <GalleryItemMobile
          key={index}
          src={item.src}
          alt={item.alt}
          text={item.text}
          destinationId={item.destinationId}
        />
      ))}
    </div>
  );

  // Desktop view with masonry-style layout
  const desktopView = (
    <div className="hidden lg:block px-1 mt-10">
      <div className="grid grid-cols-12 gap-8">
        {/* Left column */}
        <div className="col-span-3 space-y-12">
          <GalleryItem
            src={items[0].src}
            alt={items[0].alt}
            text={items[0].text}
            destinationId={items[0].destinationId}
          />
          <GalleryItem
            src={items[1].src}
            alt={items[1].alt}
            text={items[1].text}
            destinationId={items[1].destinationId}
          />
        </div>

        {/* Center column (Malaysia) */}
        <div className="col-span-3 h-[560px]">
          <GalleryItem
            src={items[2].src}
            alt={items[2].alt}
            text={items[2].text}
            featured
            destinationId={items[2].destinationId}
          />
        </div>

        {/* Right column */}
        <div className="col-span-3 space-y-12">
          <GalleryItem
            src={items[3].src}
            alt={items[3].alt}
            text={items[3].text}
            destinationId={items[3].destinationId}
          />
          <GalleryItem
            src={items[4].src}
            alt={items[4].alt}
            text={items[4].text}
            destinationId={items[4].destinationId}
          />
        </div>
        <div className="col-span-3 h-[560px]">
          <GalleryItem
            src={items[5].src}
            alt={items[5].alt}
            text={items[5].text}
            featured
            destinationId={items[5].destinationId}
          />
        </div>
      </div>
    </div>
  );

  return (
    <div className="w-full">
      {mobileView}
      {desktopView}
    </div>
  );
};

export default DestinationGallery;

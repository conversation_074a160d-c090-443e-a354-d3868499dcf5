import {
  Building,
  Utensils,
  CarFront,
  IndianRupee,
  CalendarDays,
  Check,
} from "lucide-react";
import React, { useState } from "react";
import { cn, formatIndianNumber } from "@/lib/utils";
import { X } from "lucide-react";
import { Loader } from "lucide-react";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import { useRouter } from "next/navigation";

export default function BookingDetails({ pkg }: { pkg: any }) {
  const router = useRouter();
  function pushPath(bookId: string) {
    router.push("/booking/transaction/" + bookId);
  }

  return (
    <div
      onClick={() => pushPath(pkg?.bookingId)}
      className="flex px-4 justify-center mt-[10px]"
      key={pkg?.bookingId}
    >
      <div
        className={cn(
          "flex flex-col items-center w-full h-[220px] lg:h-[255px] rounded-[14px] border-2 bg-white ring-1",
          pkg.planName === "Gold"
            ? "ring-[#EF831E]"
            : pkg.planName === "Silver"
            ? "ring-[#95A1AF]"
            : pkg.planName === "Platinum"
            ? "ring-[#CA0B0B]"
            : ""
        )}
      >
        <div className="w-full flex">
          <div className="m-[6px] h-[96px] relative w-[128px] rounded-l-lg shadow-pkg-imgShadow bg-gray-200">
            <img
              className="absolute z-0 w-full h-full rounded-l-lg"
              width={128}
              height={96}
              src={NEXT_PUBLIC_IMAGE_URL + pkg?.packageImg[0]}
              alt="package image"
            />
            <div
              className={cn(
                "absolute top-0 left-0 flex justify-center shadow-goldShadow px-2 py-1 text-xs lg:text-sm bg-goldGradient text-white rounded-tl-md rounded-br-md",
                pkg?.planName === "Gold"
                  ? "bg-goldGradient"
                  : pkg?.planName === "Silver"
                  ? "bg-silverGradient"
                  : pkg?.planName === "Platinum"
                  ? "bg-platinumGradient"
                  : ""
              )}
            >
              {pkg?.planName}
            </div>
          </div>
          <div className="w-full">
            <div className="ml-[4px] mt-[8px] flex justify-between mr-4">
              <h2 className="w-[129px] lg:w-[70%] text-[#1EC089] font-semibold non-italic text-[11px] lg:text-[14px]">
                {pkg?.packageName}
              </h2>
              <p className="w-[37px] lg:w-[45px] h-[16px] lg:h-[20px] rounded-[7px] bg-[#1EC089] pt-[3.5px] pl-[8px] text-white text-[7px] lg:text-[10px] font-semibold leading-normal font-inter">
                {pkg?.noOfNight}N/{pkg?.noOfDays}D
              </p>
            </div>
            <div className="ml-[4px] mt-[4px] flex">
              <p className="text-[#FF7865] text-[7px] lg:text-[10px] font-semibold leading-normal tracking-[0.07px]">
                Manali - 5D
              </p>
              <p className="pl-[15px] text-[#FF7865] text-[7px] lg:text-[10px] font-semibold leading-normal tracking-[0.07px]">
                Shimla - 5D
              </p>
            </div>
            <div className="flex justify-between items-center">
              <div className="ml-[8px] mt-[14px] flex justify-between w-[85px] lg:w-[140px] h-[28.5]">
                <div className="w-[21px] lg:w-[35px] h-[22.5px] flex flex-col justify-between items-center">
                  <Building className="h-[9.6px] w-[12px] lg:h-[12px] lg:w-[15px] text-[#8391A1]" />
                  <p className="text-[#8391A1] w-[21px] lg:w-[35px] h-[8px] text-[5px] lg:text-[8px] font-semibold tracking-[0.05px]">
                    {pkg?.hotelCount} Hotels
                  </p>
                </div>
                <div className="w-[21px] lg:w-[35px] h-[22.5px] flex flex-col justify-between items-center">
                  <Utensils className="h-[9.6px] w-[12px] lg:h-[12px] lg:w-[15px] text-[#8391A1]" />
                  <p className="text-[#8391A1] w-[26px] lg:w-[40px] h-[8px] text-[5px] lg:text-[8px] font-semibold tracking-[0.05px]">
                    {pkg?.activityCount} Activity
                  </p>
                </div>
                <div className="w-[21px] lg:w-[35px] h-[22.5px] flex flex-col justify-between items-center">
                  <CarFront className="h-[9.6px] w-[12px] lg:h-[12px] lg:w-[15px] text-[#8391A1]" />
                  <p className="text-[#8391A1] w-[26px] lg:w-[40px] h-[8px] text-[5px] lg:text-[8px] font-semibold tracking-[0.05px]">
                    {pkg?.vehicleCount} Vehicle
                  </p>
                </div>
              </div>
              <div className="mt-[12px] relative">
                <div className="flex items-center mr-4 mb-1">
                  <IndianRupee
                    className="text-[#FF7865] text-semibold"
                    size={15}
                  />
                  <p className="text-[#FF7865] text-[15px] lg:text-[18px] font-medium tracking-[0.06px]">
                    {formatIndianNumber(pkg?.finalPrice)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <hr className="w-[94%] mt-1 border-myBookingsLine" />
        <div className="flex flex-col items-center w-full px-4">
          <div className="flex justify-between items-center py-[13px] w-full">
            <p className="flex items-center text-[#6A778B] text-[8px] lg:text-[11px] font-normal leading-normal">
              <CalendarDays
                className="text-[#FF7865] mr-[4px] drop-shadow-pkgdoneShadow"
                size={15}
              />{" "}
              {pkg?.fullStartDate} - {pkg?.fullEndDate}
            </p>
            <button
              className={`inline-flex items-center justify-center w-[100px] lg:w-[120px] h-[26px] lg:h-[30px] px-[12px] py-[3.5px] rounded-lg text-white text-[10px] lg:text-[12px] font-medium leading-normal tracking-[0.1px] ${
                pkg?.status === "confirmed"
                  ? "bg-myBookingsBookingDoneBg"
                  : pkg?.status === "failed"
                  ? "bg-myBookingsBookingFailedBg"
                  : pkg?.status === "waiting"
                  ? "bg-myBookingsWaitingBg"
                  : pkg?.status === "pending"
                  ? "bg-myBookingsPendingBg"
                  : ""
              }`}
            >
              {pkg?.status === "confirmed" && (
                <Check size={12} className="text-white mr-[4px] font-bold" />
              )}
              {pkg?.status === "waiting" && (
                <Loader size={12} className="text-white mr-[4px] font-bold" />
              )}
              {pkg?.status === "failed" && (
                <X size={12} className="text-white mr-[4px]" />
              )}
              {pkg?.status === "pending" && (
                <X size={12} className="text-white mr-[4px]" />
              )}
              {pkg?.status}
            </button>
          </div>
          <div className="w-full">
            <button
              className={`inline-flex justify-center items-center w-full h-[40px] lg:h-[45px] px-[12px] py-[3.5px] rounded-lg border border-green-500 text-green-500 text-[12px] lg:text-[14px] font-medium leading-normal tracking-[0.1px]`}
            >
              {pkg?.status === "confirmed" && <>Booking Confirmed</>}
              {pkg?.status === "waiting" && (
                <>Payment Success - Hotel Confirmation Waiting</>
              )}
              {pkg?.status === "failed" && <>Booking Failed</>}
              {pkg?.status === "pending" && <>Payment Pending</>}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

import FilterHotelList from "./FilterHotelList";

const FilterHotel = ({ hotelCount }: { hotelCount: number }) => {
  const FilterCategories = [
    {
      label: "Filter",
    },
    {
      label: "5 ",
    },
    {
      label: "4",
    },
    {
      label: "3",
    },
    {
      label: "Pool",
    },
    {
      label: "Lowest",
    },
  ];

  return (
    <>
      <h1 className="mb-[15px] lg:mt-[10px] text-[#5D6670] text-center font-Poppins text-[18px] lg:text-[30px] font-semibold not-italic leading-normal tracking-[0.18px] ">
        Available Hotels
      </h1>
      <div className=" pl-7 flex lg:justify-center gap-[10px] overflow-x-auto whitespace-nowrap  p-2">
        {FilterCategories.map((category, index) => (
          <FilterHotelList key={index} label={category.label} />
        ))}
      </div>
    </>
  );
};

export default FilterHotel;

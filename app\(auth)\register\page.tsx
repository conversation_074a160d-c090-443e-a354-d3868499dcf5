'use client';
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import * as yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { NEXT_PUBLIC_API_URL } from '@/app/utils/constants/apiUrls';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import Image from 'next/image';
import { signInWithPopup } from 'firebase/auth';
import { auth, provider } from '@/lib/firebase.config';
import { useDispatch } from 'react-redux';
import { setAccessToken } from '@/app/store/features/authSlice';
import GoogleSignIn from '@/components/ui/gogglesignin';
import { useSession } from 'next-auth/react';
import { verifyGoogleAccessToken } from '@/app/utils/api/verifyGoogleSignin';
import { useAppSelector } from '@/app/store/store';

const registerSchema = yup.object().shape({
  email: yup
    .string()
    .email('Invalid Email Address')
    .required('Email is required'),
  password: yup
    .string()
    .matches(
      /^(?=.*[a-zA-Z])(?=.*[0-9]).{6,}$/,
      'Password must be at least 6 characters long, contain at least one uppercase letter, one lowercase letter, and one number'
    )
    .required('Password is required'),
  fullName: yup
    .string()
    .min(4, 'Full Name must be at least 4 characters long')
    .required('Full Name is required'),
  mobileNo: yup
    .string()
    .min(10, 'Mobile Number must be at least 10 digits long')
    .required('Mobile Number is required'),
  gender: yup
    .string()
    .min(4, 'Gender must be at least 4 characters long')
    .required('Gender is required'),
  // dob: yup.date().required('Date of Birth is required'),
  // address: yup.string().min(10, 'Address must be at least 10 characters long').required('Address is required'),
  // city: yup.string().min(3, 'City must be at least 3 characters long').required('City is required'),
  // pinCode: yup.string().min(6, 'Pin Code must be at least 6 characters long').required('Pin Code is required'),
});

export default function Register() {
  const router = useRouter();

  const { data } = useSession();
  const { currentPackageId } = useAppSelector((state) => state.packageDetails);

  // useEffect(() => {
  //   const verifyAccessToken = async () => {
  //     if (data?.accessToken) {
  //       //    await verifyGoogleAccessToken(data.accessToken);

  //       if (currentPackageId) {
  //         router.push('/package/' + currentPackageId + '/booking-overview');
  //       }
  //     }
  //   };
  //   verifyAccessToken();
  // }, [data?.accessToken, router, currentPackageId]);

  const dispatch = useDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: yupResolver(registerSchema),
    mode: 'onBlur',
  });

  const onSubmit = async (values: yup.InferType<typeof registerSchema>) => {
    try {
      setLoading(true);
      const response = await axios.post(
        `${NEXT_PUBLIC_API_URL}/auth/register`,
        values
      );
      toast.success('Registration Successful');
      console.log('Form submitted successfully!', response.data);

      localStorage.setItem('registeredUser', JSON.stringify(values));

      reset();

      router.push('/register/verify-otp');
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error?.response?.data?.errorMessage === 'Validation Error: email') {
        toast.error('Email id already exists');
      } else {
        toast.error('Something went wrong');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex items-center justify-center min-h-screen'>
      <div className='sm:px-5 sm:pt-5 lg:w-3/5'>
        <h1 className='text-[#FF5F5F] font-semibold text-2xl mb-6 text-center'>
          TripXplo
        </h1>
        <h1 className='text-left text-neutral-500 text-lg font-medium'>
          Create an account
        </h1>
        <GoogleSignIn />
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className='grid grid-cols-1 gap-2 sm:grid-cols-1 mt-6'>
            {/* Full Name */}
            {/* <div className="form-group mb-4">
              <input
                type="text"
                id="fullName"
                {...register('fullName')}
                className={`input-field ${errors.fullName ? 'input-error' : ''}`}
                placeholder=" "
              />
              <label htmlFor="fullName" className="input-label">
                Full Name
              </label>
              {errors.fullName && <p className="error-message">{errors.fullName.message}</p>}
            </div> */}
            {/* Email */}

            <div className='flex items-center justify-center my-4'>
              <div className='flex-grow border-t border-gray-300'></div>
              <span className='mx-4 text-gray-500'>or</span>
              <div className='flex-grow border-t border-gray-300'></div>
            </div>

            <div className='form-group mb-4'>
              <input
                type='fullName'
                id='fullName'
                {...register('fullName')}
                className={`input-field ${
                  errors.fullName ? 'input-error' : ''
                }`}
                placeholder=' '
              />
              <label htmlFor='email' className='input-label'>
                Full Name
              </label>
              {errors.fullName && (
                <p className='error-message'>{errors.fullName.message}</p>
              )}
            </div>

            <div className='form-group mb-4'>
              <input
                type='email'
                id='email'
                {...register('email')}
                className={`input-field ${errors.email ? 'input-error' : ''}`}
                placeholder=' '
              />
              <label htmlFor='email' className='input-label'>
                Email
              </label>
              {errors.email && (
                <p className='error-message'>{errors.email.message}</p>
              )}
            </div>
            {/* Password */}
            <div className='form-group mb-4'>
              <input
                type='password'
                id='password'
                {...register('password')}
                className={`input-field ${
                  errors.password ? 'input-error' : ''
                }`}
                placeholder=' '
              />
              <label htmlFor='password' className='input-label'>
                Password
              </label>
              {errors.password && (
                <p className='error-message'>{errors.password.message}</p>
              )}
            </div>

            <div className='form-group mb-4'>
              <input
                type='text'
                id='mobileNo'
                {...register('mobileNo')}
                className={`input-field ${
                  errors.mobileNo ? 'input-error' : ''
                }`}
                placeholder=' '
              />
              <label htmlFor='mobileNo' className='input-label'>
                Mobile Number
              </label>
              {errors.mobileNo && (
                <p className='error-message'>{errors.mobileNo.message}</p>
              )}
            </div>

            <div className='form-group mb-4'>
              <input
                type='text'
                id='gender'
                {...register('gender')}
                className={`input-field ${errors.gender ? 'input-error' : ''}`}
                placeholder=' '
              />
              <label htmlFor='gender' className='input-label'>
                Gender
              </label>
              {errors.gender && (
                <p className='error-message'>{errors.gender.message}</p>
              )}
            </div>

            {/* <div className="form-group mb-4">
              <input
                type="date"
                id="dob"
                {...register('dob')}
                className={`input-field ${errors.dob ? 'input-error' : ''}`}
              />
              <label htmlFor="dob" className="input-label">
                Date of Birth
              </label>
              {errors.dob && <p className="error-message">{errors.dob.message}</p>}
            </div>
            
            <div className="form-group mb-4">
              <input
                type="text"
                id="address"
                {...register('address')}
                className={`input-field ${errors.address ? 'input-error' : ''}`}
                placeholder=" "
              />
              <label htmlFor="address" className="input-label">
                Address
              </label>
              {errors.address && <p className="error-message">{errors.address.message}</p>}
            </div>
 
            <div className="form-group mb-4">
              <input
                type="text"
                id="city"
                {...register('city')}
                className={`input-field ${errors.city ? 'input-error' : ''}`}
                placeholder=" "
              />
              <label htmlFor="city" className="input-label">
                City
              </label>
              {errors.city && <p className="error-message">{errors.city.message}</p>}
            </div>
       
            <div className="form-group mb-4">
              <input
                type="text"
                id="pinCode"
                {...register('pinCode')}
                className={`input-field ${errors.pinCode ? 'input-error' : ''}`}
                placeholder=" "
              />
              <label htmlFor="pinCode" className="input-label">
                Pin Code
              </label>
              {errors.pinCode && <p className="error-message">{errors.pinCode.message}</p>}
            </div> */}
          </div>
          {/* Submit Button */}
          <div className='flex items-center justify-center mt-4'>
            <button
              type='submit'
              className='bg-gradient-to-r shadow-sm shadow-sm from-[#FF5F5F] to-[#ff5b5b] hover:bg-[#FF5F5F]/90 w-full  text-white font-medium py-3 px-4 rounded focus:outline-none '
              disabled={loading}
            >
              {loading ? 'Creating Account...' : 'Create Account'}
            </button>
          </div>
        </form>

        <div className='mt-4 flex justify-center text-muted-foreground text-sm'>
          Already a user{' '}
          <span
            onClick={() => router.push('/sign-in')}
            className=' underline decoration-[#ff5b5b] text-[#ff5b5b] pl-1 font-semibold cursor-pointer'
          >
            Login
          </span>
        </div>
      </div>
    </div>
  );
}

"use client";
import { useAuth } from "@/app/hooks/useAuth";
import { UserProfile } from "@/app/types";
import { updateProfile } from "@/app/utils/api/getProfile";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { Pencil } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { format, parseISO } from "date-fns";

const Profile = () => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [userProfile, setUserProfile] = useState<UserProfile>({
    fullName: "",
    dob: "",
    email: "",
    mobileNo: "",
    pinCode: "",
    profileImg: "",
    gender: "",
  });

  const [editFullName, setEditFullName] = useState("");
  const [editEmail, setEditEmail] = useState("");
  const [editMobileNo, setEditMobileNo] = useState("");
  const [editPinCode, setEditPinCode] = useState("");
  const [editDob, setEditDob] = useState<string | undefined>(undefined);
  const [editGender, setEditGender] = useState("");

  useEffect(() => {
    if (user) {
      setUserProfile({
        fullName: user.fullName || "",
        dob: user.dob || "",
        email: user.email || "",
        mobileNo: user.mobileNo || "",
        pinCode: user.pinCode || "",
        profileImg: user.profileImg || "",
        gender: user.gender || "",
      });
      setEditFullName(user.fullName || "");
      setEditEmail(user.email || "");
      setEditMobileNo(user.mobileNo || "");
      setEditPinCode(user.pinCode || "");
      setEditDob(user.dob || "");
      setEditGender(user.gender || "");
    }
  }, [user]);

  const handleFieldChange =
    (setEditField: React.Dispatch<React.SetStateAction<string>>) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setEditField(event.target.value);
    };

  const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEditDob(event.target.value);
  };

  const handleUpdateField = async (field: keyof UserProfile, value: string) => {
    try {
      const updatedProfile = { ...userProfile, [field]: value };
      const response = await updateProfile(updatedProfile);
      if (response.success) {
        toast.success("Profile updated successfully");
        router.refresh();
        setUserProfile(updatedProfile);
      } else {
        toast.error("Error updating profile");
      }
    } catch (error) {
      toast.error("Error updating profile");
      console.error(`Error updating ${field}:`, error);
    }
  };

  const handleUpdateDate = async () => {
    if (editDob) {
      await handleUpdateField("dob", editDob);
    }
  };

  const formattedDate = userProfile.dob
    ? format(parseISO(userProfile.dob), "PPP")
    : "N/A";

  return (
    <div className="min-h-screen w-full flex justify-center items-start bg-gray-50 px-4 py-8 lg:py-28">
      <div className="w-full max-w-4xl">
        {user && (
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden lg:p-8 p-4">
            {/* Profile Header */}
            <div className="flex flex-col items-center space-y-6 lg:space-y-8 mb-8">
              <div className="relative lg:w-40 lg:h-40 w-24 h-24 rounded-full">
                <Image
                  unoptimized
                  src={user.profileImg}
                  className="object-cover rounded-full absolute"
                  fill
                  alt="Profile"
                />
              </div>
              <hr className="border-t w-full border-grey-200" />
            </div>

            {/* Profile Fields Container */}
            <div className="grid lg:grid-cols-2 gap-6 lg:gap-8">
              {/* Name Field */}
              <div className="bg-gray-50 rounded-xl p-6 ">
                <div className="flex justify-between items-center">
                  <div>
                    <h1 className="text-slate-600 text-sm lg:text-base">
                      Name
                    </h1>
                    <h1 className="text-[#FF5F5F] drop-shadow-black drop-shadow-sm text-xl lg:text-2xl font-medium">
                      {userProfile.fullName}
                    </h1>
                  </div>
                  <Drawer>
                    <DrawerTrigger>
                      <div className="p-2 lg:p-3 rounded-full bg-[#ff5f5f] flex items-center justify-center text-white">
                        <Pencil className="lg:w-4 lg:h-4 w-3 h-3" />
                      </div>
                    </DrawerTrigger>
                    <DrawerContent>
                      <div className="p-6">
                        <h2 className="text-2xl font-semibold mb-4">
                          Edit Name
                        </h2>
                        <input
                          type="text"
                          value={editFullName}
                          onChange={handleFieldChange(setEditFullName)}
                          className="border p-3 w-full mb-4 rounded-lg"
                          placeholder="Enter new name"
                        />
                        <button
                          onClick={() =>
                            handleUpdateField("fullName", editFullName)
                          }
                          className="bg-[#ff5f5f] text-white px-6 py-3 rounded-full w-full lg:text-lg"
                        >
                          Update Name
                        </button>
                      </div>
                    </DrawerContent>
                  </Drawer>
                </div>
              </div>

              {/* Email Field */}
              <div className="bg-gray-50 rounded-xl p-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h1 className="text-slate-600 text-sm lg:text-base">
                      Email
                    </h1>
                    <h1 className="text-[#FF5F5F] drop-shadow-black drop-shadow-sm text-xl lg:text-2xl font-medium truncate max-w-[200px] lg:max-w-[300px]">
                      {userProfile.email}
                    </h1>
                  </div>
                  <Drawer>
                    <DrawerTrigger>
                      <div className="p-2 lg:p-3 rounded-full bg-[#ff5f5f] flex items-center justify-center text-white">
                        <Pencil className="lg:w-4 lg:h-4 w-3 h-3" />
                      </div>
                    </DrawerTrigger>
                    <DrawerContent>
                      <div className="p-6">
                        <h2 className="text-2xl font-semibold mb-4">
                          Edit Email
                        </h2>
                        <input
                          type="email"
                          value={editEmail}
                          onChange={handleFieldChange(setEditEmail)}
                          className="border p-3 w-full mb-4 rounded-lg"
                          placeholder="Enter new email"
                        />
                        <button
                          onClick={() => handleUpdateField("email", editEmail)}
                          className="bg-[#ff5f5f] text-white px-6 py-3 rounded-full w-full lg:text-lg"
                        >
                          Update Email
                        </button>
                      </div>
                    </DrawerContent>
                  </Drawer>
                </div>
              </div>

              {/* Mobile Number Field */}
              <div className="bg-gray-50 rounded-xl p-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h1 className="text-slate-600 text-sm lg:text-base">
                      Mobile Number
                    </h1>
                    <h1 className="text-[#FF5F5F] drop-shadow-black drop-shadow-sm text-xl lg:text-2xl font-medium">
                      {userProfile.mobileNo}
                    </h1>
                  </div>
                  <Drawer>
                    <DrawerTrigger>
                      <div className="p-2 lg:p-3 rounded-full bg-[#ff5f5f] flex items-center justify-center text-white">
                        <Pencil className="lg:w-4 lg:h-4 w-3 h-3" />
                      </div>
                    </DrawerTrigger>
                    <DrawerContent>
                      <div className="p-6">
                        <h2 className="text-2xl font-semibold mb-4">
                          Edit Mobile Number
                        </h2>
                        <input
                          type="text"
                          value={editMobileNo}
                          onChange={handleFieldChange(setEditMobileNo)}
                          className="border p-3 w-full mb-4 rounded-lg"
                          placeholder="Enter new mobile number"
                        />
                        <button
                          onClick={() =>
                            handleUpdateField("mobileNo", editMobileNo)
                          }
                          className="bg-[#ff5f5f] text-white px-6 py-3 rounded-full w-full lg:text-lg"
                        >
                          Update Mobile Number
                        </button>
                      </div>
                    </DrawerContent>
                  </Drawer>
                </div>
              </div>

              {/* Pin Code Field */}
              <div className="bg-gray-50 rounded-xl p-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h1 className="text-slate-600 text-sm lg:text-base">
                      Pin Code
                    </h1>
                    <h1 className="text-[#FF5F5F] drop-shadow-black drop-shadow-sm text-xl lg:text-2xl font-medium">
                      {userProfile.pinCode}
                    </h1>
                  </div>
                  <Drawer>
                    <DrawerTrigger>
                      <div className="p-2 lg:p-3 rounded-full bg-[#ff5f5f] flex items-center justify-center text-white">
                        <Pencil className="lg:w-4 lg:h-4 w-3 h-3" />
                      </div>
                    </DrawerTrigger>
                    <DrawerContent>
                      <div className="p-6">
                        <h2 className="text-2xl font-semibold mb-4">
                          Edit Pin Code
                        </h2>
                        <input
                          type="text"
                          value={editPinCode}
                          onChange={handleFieldChange(setEditPinCode)}
                          className="border p-3 w-full mb-4 rounded-lg"
                          placeholder="Enter new pin code"
                        />
                        <button
                          onClick={() =>
                            handleUpdateField("pinCode", editPinCode)
                          }
                          className="bg-[#ff5f5f] text-white px-6 py-3 rounded-full w-full lg:text-lg"
                        >
                          Update Pin Code
                        </button>
                      </div>
                    </DrawerContent>
                  </Drawer>
                </div>
              </div>

              {/* Date of Birth Field */}
              <div className="bg-gray-50 rounded-xl p-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h1 className="text-slate-600 text-sm lg:text-base">
                      Date of Birth
                    </h1>
                    <h1 className="text-[#FF5F5F] drop-shadow-black drop-shadow-sm text-xl lg:text-2xl font-medium">
                      {formattedDate}
                    </h1>
                  </div>
                  <AlertDialog>
                    <AlertDialogTrigger>
                      <div className="p-2 lg:p-3 rounded-full bg-[#ff5f5f] flex items-center justify-center text-white">
                        <Pencil className="lg:w-4 lg:h-4 w-3 h-3" />
                      </div>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <div className="p-6">
                        <h2 className="text-2xl font-semibold mb-4">
                          Edit Date of Birth
                        </h2>
                        <input
                          type="date"
                          value={editDob}
                          onChange={handleDateChange}
                          className="border p-3 w-full mb-4 rounded-lg"
                        />
                        <AlertDialogAction
                          onClick={handleUpdateDate}
                          className="bg-[#ff5f5f] text-white px-6 py-3 rounded-full w-full lg:text-lg"
                        >
                          Update Date of Birth
                        </AlertDialogAction>
                      </div>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;

"use client";

import { setFilterCategory } from "@/app/store/features/filterCategorySlice";
import { useAppSelector } from "@/app/store/store";
import { cn } from "@/lib/utils";
import React, { useEffect } from "react";
import { useDispatch } from "react-redux";

interface FilterCategoryListProps {
  label: string;
}
const FilterCategoryList = ({ label }: FilterCategoryListProps) => {
  const dispatch = useDispatch();
  const category = useAppSelector(
    (state) => state.filterCategory.filterCategory
  );

  useEffect(() => {
    dispatch(setFilterCategory("All Packages"));
  }, [dispatch]);
  return (
    <div className="mt-2">
      <div
        onClick={() => dispatch(setFilterCategory(label))}
        className={cn(
          "border border-neutral-200 text-neutral-400 bg-[FFF] font-Poppins font-medium not-italic leading-normal shadow-sm sm:rounded-[5px] md:rounded-[8px] px-3 py-2 mr-1 lg:px-5 lg:py-3",
          category === label &&
            "bg-[#FFF] stroke-[#27B182] border border-[#27B182] text-[#1EC089] filter drop-shadow"
        )}
      >
        <div>
          {category !== null && (
            <div className="flex text-[12px] md:text-[16px] lg:text-[16px]">
              {label}
              <div className="hidden">{category}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FilterCategoryList;

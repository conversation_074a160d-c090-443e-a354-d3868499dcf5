"use client";
import BookingDetails from "./BookingDetails";
import React, { useState } from "react";

import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";

import { useInfiniteScroll } from "@/app/hooks/useInfiniteScroll";
import { useBookingList } from "@/app/hooks/useBookingList";
import PackagesLoading from "@/app/(user-area)/components/loading/PackagesLoading";

const ExploreFilter = () => {
  const router = useRouter();

  function clickBack() {
    router.push("/");
  }

  const [offset, setOffset] = useState<number>(0);
  const { bookingList, isLoading, bookingListHasNext, err } =
    useBookingList(offset);

  const fetchMoreItems = () => {
    if (bookingListHasNext) {
      setOffset(offset + 10);
    }
  };

  const lastElementRef = useInfiniteScroll({
    hasMore: bookingListHasNext,
    onLoadMore: fetchMoreItems,
  });

  return (
    <div>
      <div
        className="fixed top-0 text-center flex items-center  w-full h-[100px] bg-white z-50"
        style={{ boxShadow: "0px 4px 36.1px 0px rgba(190, 190, 190, 0.22)" }}
      >
        <span className="ml-[40px]">
          <button onClick={clickBack}>
            <ArrowLeft className="h-[33px] w-[33px] text-[#FF5F5F]" />
          </button>
        </span>
        <h1
          className="text-center ml-[16px]  font-Poppins text-[18px] not-italic font-semibold leading-normal tracking-[0.18px]"
          style={{
            textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.20)",
            backgroundImage:
              "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          My Bookings
        </h1>
      </div>
      <div>{/* <BookingFilter /> */}</div>
      <div className="my-[100px] lg:my-[120px] lg:grid lg:grid-cols-3">
        {bookingList.length > 0 &&
          bookingList.map((booking: any, index: number) => (
            <div
              key={booking?.bookingId}
              ref={index === bookingList.length - 1 ? lastElementRef : null}
            >
              <BookingDetails pkg={booking} />
            </div>
          ))}
        {!isLoading && bookingList?.length < 1 && (
          <p className="font-semibold flex items-center justify-center py-10 text-red-400">
            No Booking found
          </p>
        )}
        {isLoading && <PackagesLoading />}
      </div>
    </div>
  );
};

export default ExploreFilter;

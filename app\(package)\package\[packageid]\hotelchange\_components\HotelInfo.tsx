"use client";
import React from "react";
import HotelInfoList from "./HotelInfoList";
import { HotelMeal } from "@/app/types/pack";
import { useSelector } from "react-redux";
import { HotelChangeDataType } from "@/app/types/hotel";

const FilterCards = ({ hotelData }: { hotelData: HotelChangeDataType[] }) => {
  const prevHotel = useSelector(
    (store: any) => store.hotelChange?.replaceHotel
  );
  console.log(hotelData, "hotel");
  const filteredHotels = hotelData.filter((hotel) => {
    const selectedRoom = hotel.hotelRoom?.find(
      (data) => data?.mealPlan?.length > 0
    );
    if (!selectedRoom) return false;

    const selectedMealPlan = selectedRoom.mealPlan.find((data) =>
      prevHotel?.mealPlan?.includes(data.mealPlan)
    );
    return !!selectedMealPlan;
  });

  return (
    // <div className="px-2 py-3">
    <div className="px-2 lg:px-10 flex-col lg:grid lg:grid-cols-3  lg:gap-4 items-center my-3">
      {filteredHotels.map((hotel, index) => (
        <div key={index} className="">
          <HotelInfoList
            hotel={hotel}
            startEndDate={`${prevHotel?.fullStartDate} - ${prevHotel?.fullEndDate}`}
            prevHotel={prevHotel}
          />
        </div>
      ))}
    </div>
    // </div>
  );
};

export default FilterCards;

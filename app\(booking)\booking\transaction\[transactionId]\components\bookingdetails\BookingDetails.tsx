import React from "react";
import { FaIndianRupeeSign } from "react-icons/fa6";
import { CalendarDays } from "lucide-react";
import { Check } from "lucide-react";
import { Circle } from "lucide-react";
import { PiFlagPennantLight } from "react-icons/pi";
import Image from "next/image";
import {
  Building,
  IndianRupee,
  CircleUserRound,
  MapPin,
  Clock,
} from "lucide-react";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import { useSelector } from "react-redux";
import { Room } from "@/app/hooks/usePackageList";

const BookingDetails = ({ events , pack}:any) => {
  const roomCapacityData: Room = useSelector(
    (store: any) => store.roomSelect.room
  );
  return (
    <>
      <section className="  px-10 py-10">
        <h1 className="font-Poppins py-5  text-center  text-[#5D6670] text-lg font-semibold">
          Your Package Booking
        </h1>
        <div
          className="h-auto border w-full py-5 px-5 bg-[#FFF] rounded-[14px]"
          style={{
            border: "var(--adult, 2px) solid rgba(172, 161, 159, 0.34)",
          }}
        >
          <p className="text-[#5D6670] text-xs font-medium font-Poppins flex items-cente justify-center">
            Booking Id : {pack?.bookingId}
          </p>
          <hr
            className="my-3 px-4"
            style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
          />
          <div className=" flex">
            <div className="w-1/2  h-auto">
              <Image
                src={NEXT_PUBLIC_IMAGE_URL + pack?.packageImg[0] }
                width={100}
                height={100}
                objectFit="cover"
                //layout="responsive"
                className="object-cover h-[70px] w-[90px] rounded-lg"
                style={{
                  boxShadow: "2px 4px 13.2px 0px rgba(0, 0, 0, 0.06)",
                }}
                alt="packageimage"
              />
            </div>
            <div className="flex flex-col">
              <h1
                className="h-auto flex-wrap text-[13px]  px-2 font-semibold"
                style={{
                  backgroundImage:
                    "   linear-gradient(87deg, #FF5F5F -25.84%, #FF5F5F -25.82%, #FF9080 118.31%)",
                  backgroundClip: "text",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                }}
              >
                {pack?.packageName}
              </h1>
              <div className="flex gap-4 flex-wrap px-2  py-2">
                <div className="flex flex-col items-center justify-center gap-2">
                  <span>
                    <Building className="w-[10px] h-[10px]" />
                  </span>
                  <p className="text-[#8391A1] text-[5px] font-semibold font-Poppins">
                    {pack?.hotelCount} Hotels
                  </p>
                </div>
                <div className="flex flex-col items-center justify-center gap-2">
                  <span>
                    <Building className="w-[10px] h-[10px]" />
                  </span>
                  <p className="text-[#8391A1] text-[5px] font-semibold font-Poppins">
                    {pack?.activityCount} Activity
                  </p>
                </div>

                <div className="flex flex-col  items-center justify-center gap-2">
                  <h2 className="text-[#6A778B] text-[6px] font-medium font-Poppins">
                    Starts from
                  </h2>
                  <span className="text-[#27B182] flex items-center gap-2 text-[7px] font-semibold">
                    <PiFlagPennantLight className="w-[10px] h-[10px]" />
                    {pack?.startFrom}
                  </span>
                </div>

                <div className="flex flex-col items-center justify-center">
                  <span className="flex items-center text-[#6A778B] font-montserrat font-semibold text-[16px] ">
                    <IndianRupee className="w-[10px] h-[10px]" />
                    {pack?.finalPrice}
                  </span>
                  <p className="text-[#8391A1] font-Poppins text-[6px] font-medium">
                    Package
                  </p>
                </div>
              </div>
            </div>
          </div>
          <hr
            className="my-3 px-4"
            style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
          />
          <div className="flex  flex-col">
            <div className="flex py-2 flex-wrap ">
            <div className="p-1 border border-neutral-100 shadow-sm rounded-lg">
          <MapPin size={14} className="text-[#FF5F5F]" />
        </div>
        <div className="flex items-center gap-1">
  {pack?.destination
    ?.filter((dest :any)=> dest.noOfNight > 0)
    ?.map((dest : any, i:any, arr:any) => (
      <div className="flex items-center text-nowrap" key={i}>
        <div className="flex items-center text-[#FF7865] font-Poppins text-[12px] font-medium">
          &nbsp;{dest?.noOfNight}N
        </div>
        <div className="flex items-center text-[#6A778B] font-Poppins text-[12px] font-medium">
          &nbsp;-&nbsp;{dest?.destinationName}
        </div>
        {i !== arr.length - 1 && <span className="text-[#6A778B]"> &nbsp;| &nbsp;</span>}
      </div>
    ))}
</div>
              <div className="flex items-center gap-2 ml-auto pr-10 ">
                <Clock className="w-[10px] h-[10px] text-[#FF7865]" />
                <h1 className="text-[#6A778B] font-medium font-Poppins text-[10px]">
                  {pack?.noOfNight}N/{pack?.noOfDays}D
                </h1>
              </div>
            </div>
            <div className="flex items-center py-2 gap-2">
              <CircleUserRound className="w-[10px] h-[10px] text-[#FF7865]" />
              <p className="text-[#6A778B] font-medium text-[10px]">
                {roomCapacityData?.totalAdults} Adults + {roomCapacityData?.totalChilds} Child
              </p>
            </div>
            <div className="flex items-center py-2 gap-2">
              <CalendarDays className="w-[10px] h-[10px] text-[#FF7865]" />
              <p className="text-[#6A778B] font-medium text-[10px]">
                {pack?.fullStartDate} - {pack?.fullEndDate}
              </p>
            </div>
          </div>
          <hr
            className="my-3 px-4"
            style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
          />
          <section className="py-3">
            <div className="">
              {events.map((event: any, key:any) => (
                <>
                  <div key={key} className="flex  ">
                    <div className="flex flex-col items-center ">
                      <Circle
                        size={12}
                        style={{
                          fill: "linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)",
                          background: "green-400",
                        }}
                      />
                      {key !== events.length - 1 && (
                        <div
                          className="h-[40px] "
                          style={{ borderLeft: pack.balanceAmount === 0 || key != 1 ? "2px solid #1EC089" : "2px dashed #1EC089" }}
                        >
                          {" "}
                        </div>
                      )}
                    </div>
                    <div className="px-3">
                      <EventCards
                        heading={event.heading}
                        subHeading={event.subHeading}
                      />
                    </div>
                  </div>
                </>
              ))}
            </div>
          </section>
        </div>
      </section>
    </>
  );
};

const EventCards = ({ heading, subHeading }:any) => {
  return (
    <div className="flex flex-col     ">
      <div
        className="font-Poppins  text-xs not-italic font-semibold leading-normal tracking-[0.12px]  bg-clip-text text-transparent"
        style={{
          backgroundImage:
            " linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)",
        }}
      >
        {heading}
      </div>
      <div className="text-[#8391A1] font-Poppins text-[10px] font-normal not-italic leading-normal tracking-[0.1px]">
        {subHeading}
      </div>
    </div>
  );
};

export default BookingDetails;
// import Image from "next/image";
// import Link from "next/link";
// import {
//   Building,
//   Utensils,
//   CarFront,
//   IndianRupee,
//   ArrowLeft,
//   CalendarDays,
//   Check,
//   CircleUserRound,
//   UtensilsIcon,
//   MapPin,
//   Calendar,
//   Clock,
// } from "lucide-react";
// import {
//   Popover,
//   PopoverContent,
//   PopoverTrigger,
// } from "@/components/ui/popover";
// import {
//   Drawer,
//   DrawerClose,
//   DrawerContent,
//   DrawerFooter,
//   DrawerHeader,
//   DrawerTitle,
//   DrawerTrigger,
// } from "@/components/ui/drawer";
// import { PiFlagPennantLight } from "react-icons/pi";
// // import PaymentOptions from "./PaymentOptions";
// import FareBreakup from "../(package)/package/_components/FareBreakup";
// import { ChevronDown } from "lucide-react";
// import { FaIndianRupeeSign } from "react-icons/fa6";
// // import { useRouter } from "next/navigation";
// export default function PackageBooking({}: //   price,
// //   adult,
// //   child,
// //   packagePrice,
// //   gstPrice,
// //   gstPer,
// {
//   //   price: number;
//   //   adult: number;
//   //   child: number;
//   //   packagePrice: number;
//   //   gstPrice: number;
//   //   gstPer: number;
// }) {
//   return (
//     <div>
//       <div
//         className="fixed top-0 text-center flex items-center  w-full h-[100px] bg-white z-10"
//         style={{ boxShadow: "0px 4px 36.1px 0px rgba(190, 190, 190, 0.22)" }}
//       >
//         <span className="ml-[40px]">
//           <Link href={"/"}>
//             <button>
//               <ArrowLeft className="h-[33px] w-[33px] text-[#FF5F5F]" />
//             </button>
//           </Link>
//         </span>
//         <h1
//           className="text-center ml-[16px]  font-Poppins text-[18px] not-italic font-semibold leading-normal tracking-[0.18px]"
//           style={{
//             textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.20)",
//             backgroundImage:
//               "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
//             backgroundClip: "text",
//             WebkitBackgroundClip: "text",
//             WebkitTextFillColor: "transparent",
//           }}
//         >
//           Select roomSize & Guests
//         </h1>
//       </div>
//       <div className="pt-[160px] pb-40">
//         <h1 className="font-Poppins  text-center  text-[#5D6670] text-lg font-semibold">
//           Your Package Booking
//         </h1>
//         <section className="  px-10 py-10">
//           <div
//             className="h-auto border w-full py-5 px-5 bg-[#FFF] rounded-[14px]"
//             style={{
//               border: "var(--adult, 2px) solid rgba(172, 161, 159, 0.34)",
//             }}
//           >
//             <p className="text-[#5D6670] text-xs font-medium font-Poppins flex items-cente justify-center">
//               Booking Id : 5NMAN04SL0201
//             </p>
//             <hr
//               className="my-3 px-4"
//               style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
//             />
//             <div className=" flex">
//               <div className="w-1/2  h-auto">
//                 <Image
//                   src="https://images.pexels.com/photos/2166559/pexels-photo-2166559.jpeg?auto=compress&cs=tinysrgb&w=600"
//                   width={100}
//                   height={100}
//                   objectFit="cover"
//                   //layout="responsive"
//                   className="object-cover h-[70px] w-[90px] rounded-lg"
//                   style={{
//                     boxShadow: "2px 4px 13.2px 0px rgba(0, 0, 0, 0.06)",
//                   }}
//                   alt="packageimage"
//                 />
//               </div>
//               <div className="flex flex-col">
//                 <h1
//                   className="h-auto flex-wrap text-[13px]  px-2 font-semibold"
//                   style={{
//                     backgroundImage:
//                       "   linear-gradient(87deg, #FF5F5F -25.84%, #FF5F5F -25.82%, #FF9080 118.31%)",
//                     backgroundClip: "text",
//                     WebkitBackgroundClip: "text",
//                     WebkitTextFillColor: "transparent",
//                   }}
//                 >
//                   Enchanting Manali 6 Day Himalayan
//                 </h1>
//                 <div className="flex gap-4 flex-wrap px-2  py-2">
//                   <div className="flex flex-col items-center justify-center gap-2">
//                     <span>
//                       <Building className="w-[10px] h-[10px]" />
//                     </span>
//                     <p className="text-[#8391A1] text-[5px] font-semibold font-Poppins">
//                       2 Hotels
//                     </p>
//                   </div>
//                   <div className="flex flex-col items-center justify-center gap-2">
//                     <span>
//                       <Building className="w-[10px] h-[10px]" />
//                     </span>
//                     <p className="text-[#8391A1] text-[5px] font-semibold font-Poppins">
//                       2 Hotels
//                     </p>
//                   </div>

//                   <div className="flex flex-col  items-center justify-center gap-2">
//                     <h2 className="text-[#6A778B] text-[6px] font-medium font-Poppins">
//                       Starts from
//                     </h2>
//                     <span className="text-[#27B182] flex items-center gap-2 text-[7px] font-semibold">
//                       <PiFlagPennantLight className="w-[10px] h-[10px]" />
//                       Ooty
//                     </span>
//                   </div>

//                   <div className="flex flex-col items-center justify-center">
//                     <span className="flex items-center text-[#6A778B] font-montserrat font-semibold text-[16px] ">
//                       <IndianRupee className="w-[10px] h-[10px]" />
//                       8,999
//                     </span>
//                     <p className="text-[#8391A1] font-Poppins text-[6px] font-medium">
//                       Package
//                     </p>
//                   </div>
//                 </div>
//               </div>
//             </div>
//             <hr
//               className="my-3 px-4"
//               style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
//             />
//             <div className="flex  flex-col">
//               <div className="flex py-2 flex-wrap ">
//                 <div className="flex items-center font-Poppins text-[10px] font-medium gap-2">
//                   <MapPin className="w-[10px] h-[10px] text-[#FF7865]" />
//                   <p className="text-[#FF7865]">3N</p>
//                   <h1 className="text-[#6A778B]">- Manali</h1>
//                   <h2>|</h2>
//                   <p className="text-[#FF7865]">3N</p>
//                   <h1 className="text-[#6A778B]">- Manali</h1>
//                 </div>
//                 <div className="flex items-center gap-2 ml-auto pr-10 ">
//                   <Clock className="w-[10px] h-[10px] text-[#FF7865]" />
//                   <h1 className="text-[#6A778B] font-medium font-Poppins text-[10px]">
//                     5N/6D
//                   </h1>
//                 </div>
//               </div>
//               <div className="flex items-center py-2 gap-2">
//                 <CircleUserRound className="w-[10px] h-[10px] text-[#FF7865]" />
//                 <p className="text-[#6A778B] font-medium text-[10px]">
//                   2 Adults + 1 Child
//                 </p>
//               </div>
//               <div className="flex items-center py-2 gap-2">
//                 <CalendarDays className="w-[10px] h-[10px] text-[#FF7865]" />
//                 <p className="text-[#6A778B] font-medium text-[10px]">
//                   Wed, 12 Jun - Fri, 14 Jun
//                 </p>
//               </div>
//             </div>
//             <hr
//               className="my-3 px-4"
//               style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
//             />
//             {/* <h1
//               style={{
//                 backgroundImage:
//                   "   linear-gradient(87deg, #FF5F5F -25.84%, #FF5F5F -25.82%, #FF9080 118.31%)",
//                 backgroundClip: "text",
//                 WebkitBackgroundClip: "text",
//                 WebkitTextFillColor: "transparent",
//               }}
//               className="font-Poppins text-[14px] text-center font-semibold"
//             >
//               Payment Options
//             </h1>
//             <div className="px-15 py-5 flex flex-col gap-8">
//               <section
//                 className="h-auto border w-full py-5  bg-[#FFF] rounded-[14px]"
//                 style={{
//                   border: "var(--adult, 2px) solid rgba(172, 161, 159, 0.34)",
//                 }}
//               >
//                 <label className="flex item-center gap-2 px-4">
//                   <input type="radio" />
//                   <h1 className="text-[#5D6670] font-Poppins flex items-center text-xs font-semibold">
//                     Reserver for{" "}
//                     <IndianRupee className="w-[10px] h-[10px] mx-1" /> 1
//                   </h1>
//                 </label>
//                 <hr
//                   className="my-3 mx-4"
//                   style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
//                 />
//                 <div className="flex items-center py-2  px-4">
//                   <span className="bg-[#D0DDEC] rounded-full px-2  py-1 text-xs">
//                     1
//                   </span>
//                   <div className="flex flex-col  pl-4 font-Poppins  py-1 font-semibold">
//                     <h1 className="text-[#5D6670] py-1 text-[11px] ">
//                       Book Your Slot
//                     </h1>
//                     <p className="text-[#1EC089]   text-[8px]">
//                       Pay just 1 and book your travel
//                     </p>
//                   </div>
//                   <span className="text-[#5D6670] ml-auto text-xs pr-5 flex items-center  font-montserrat font-semibold">
//                     <IndianRupee className="w-[10px] h-[10px]" />1
//                   </span>
//                 </div>
//                 <div className="flex items-center  px-4">
//                   <span className="bg-[#D0DDEC] rounded-full px-2  py-1 text-xs">
//                     2
//                   </span>
//                   <div className="flex flex-col  pl-4 font-Poppins  py-1 font-semibold">
//                     <h1 className="text-[#5D6670] py-1 text-[11px]">
//                       15 Days before Tour
//                     </h1>
//                     <p className="text-[#1EC089]   text-[8px]">12-8-2024</p>
//                   </div>
//                   <span className="text-[#5D6670] ml-auto pr-5 text-xs flex items-center  font-montserrat font-semibold">
//                     <IndianRupee className="w-[10px] h-[10px]" />
//                     8999
//                   </span>
//                 </div>
//               </section>
//             </div>
//             <div className="px-15 py-5 flex flex-col gap-8">
//               <section
//                 className="h-auto border w-full py-5  bg-[#FFF] rounded-[14px]"
//                 style={{
//                   border: "var(--adult, 2px) solid rgba(172, 161, 159, 0.34)",
//                 }}
//               >
//                 <label className="flex item-center gap-2 px-4">
//                   <input type="radio" />
//                   <h1 className="text-[#5D6670] font-Poppins flex items-center text-xs font-semibold">
//                     Reserver for{" "}
//                     <IndianRupee className="w-[10px] h-[10px] mx-1" /> 1
//                   </h1>
//                 </label>
//                 <hr
//                   className="my-3 mx-4"
//                   style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
//                 />
//                 <div className="flex items-center py-2  px-4">
//                   <span className="bg-[#D0DDEC] rounded-full px-2  py-1 text-xs">
//                     1
//                   </span>
//                   <div className="flex pl-4 font-Poppins  py-1 font-semibold">
//                     <h1 className="text-[#5D6670] py-1 text-[9px] flex items-center flex-wrap ">
//                       Book your travel by paying full amount and{" "}
//                       <p className="text-[#1EC089] ">Get 10X Reward Coins</p>
//                     </h1>
//                   </div>
//                   <span className="text-[#5D6670] ml-auto text-xs pr-5 flex items-center  font-montserrat font-semibold">
//                     <IndianRupee className="w-[10px] h-[10px]" />1
//                   </span>
//                 </div>
//               </section>
//             </div>
//             <hr
//               className="my-3 px-4"
//               style={{ stroke: "1px solid rgba(0, 0, 0, 0.08)" }}
//             />
//             <section className="flex flex-col gap-2">
//               <div>
//                 <h1 className="text-[#4A5058] py-2 font-Poppins text-[11px] font-medium">
//                   Have a coupon code ?
//                 </h1>
//                 <label
//                   className="rounded-lg flex  items-center w-1/2 p-2 "
//                   style={{ border: " 1px solid rgba(0, 0, 0, 0.23)" }}
//                 >
//                   <input
//                     type="text"
//                     placeholder="Enter Code"
//                     className="outline-none text-xs px-2"
//                   />
//                   <button
//                     className="text-[8px] px-4 font-Poppins font-semibold"
//                     style={{
//                       background:
//                         "linear-gradient(90deg, #27B182 -5.26%, #31DAA1 99.73%)",
//                       backgroundClip: "text",
//                       WebkitBackgroundClip: "text",
//                       WebkitTextFillColor: "transparent",
//                     }}
//                   >
//                     Apply
//                   </button>
//                 </label>
//               </div>
//               <div>
//                 <h1 className="text-[#4A5058] py-2 font-Poppins text-[11px] font-medium">
//                   Redeem Coins
//                 </h1>
//                 <label
//                   className="rounded-lg  flex items-center w-1/2 p-2"
//                   style={{
//                     boxShadow: "2px 4px 20.9px 0px rgba(101, 255, 200, 0.22)",
//                     border: "1px solid var(--grd-red, #FF5F5F)",
//                   }}
//                 >
//                   <input
//                     type="text"
//                     placeholder="1000 claimable"
//                     className="outline-none text-xs px-2"
//                   />
//                   <button
//                     className="text-[8px] px-4 font-Poppins font-semibold"
//                     style={{
//                       background:
//                         " linear-gradient(87deg, #FF5F5F -25.84%, #FF5F5F -25.82%, #FF9080 118.31%)",
//                       backgroundClip: "text",
//                       WebkitBackgroundClip: "text",
//                       WebkitTextFillColor: "transparent",
//                     }}
//                   >
//                     Apply
//                   </button>
//                 </label>
//               </div>
//             </section> */}
//           </div>
//         </section>
//       </div>
//       <div className="shadowTop fixed bottom-0 right-0 left-0 z-10 w-full bg-white">
//         <div className="px-5">
//           <div className="flex justify-center">
//             <hr className="w-[244px] h-[4px] rounded-[22px] bg-bookLineBg text-center" />
//           </div>

//           <div className="flex justify-between items-center my-5">
//             <div className="flex flex-col gap-1">
//               <Drawer>
//                 <DrawerTrigger>
//                   <div>
//                     <button className="flex items-center font-semibold bg-neutral-100 rounded-lg text-[10px] whitespace-nowrap gap-1 px-2 py-1 border text-[#5D6670]">
//                       Fare Details
//                       <ChevronDown size={14} className="stroke-2" />
//                     </button>
//                   </div>
//                 </DrawerTrigger>

//                 <DrawerContent>
//                   <DrawerHeader>
//                     <DrawerTitle>
//                       {/* <FareBreakup
//                       // adults={adult}
//                       // child={child}
//                       // packagePrice={packagePrice}
//                       // gstPer={gstPer}
//                       // gstPrice={gstPrice}
//                       /> */}
//                     </DrawerTitle>
//                   </DrawerHeader>
//                   <DrawerFooter>
//                     <DrawerClose />
//                   </DrawerFooter>
//                 </DrawerContent>
//               </Drawer>
//               <div className="flex items-center gap-1">
//                 <h1 className="text-2xl font-semibold flex items-center text-[#5D6670]">
//                   <FaIndianRupeeSign size={14} />
//                   {/* {price} */}
//                 </h1>
//                 <h1 className="text-[#FF5F5F] small-text whitespace-nowrap">
//                   per Package
//                 </h1>
//               </div>
//             </div>
//             <button
//               //   onClick={packageBooking}
//               className="w-[120px] h-[40px] bg-bookNowBg shadow-bookNowShadow rounded-[30px] text-white text-[14px] font-semibold leading-normal tracking-[0.14px]"
//             >
//               Book Now
//             </button>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }
import React from 'react';
import { MapPin } from 'lucide-react';

interface WherePanelProps {
  onLocationSelect: (location: string) => void;
}

export const WherePanel: React.FC<WherePanelProps> = ({ onLocationSelect }) => {
  const popularDestinations = [
    'Paris, France',
    'Tokyo, Japan',
    'New York, USA',
    'London, UK',
    'Rome, Italy',
    'Barcelona, Spain',
    'Amsterdam, Netherlands',
    'Sydney, Australia'
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Where to?</h3>
      
      <div className="grid grid-cols-2 gap-3">
        {popularDestinations.map((destination) => (
          <button
            key={destination}
            onClick={() => onLocationSelect(destination)}
            className="flex items-center gap-3 p-3 text-left hover:bg-gray-50 rounded-lg transition-colors"
          >
            <MapPin className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-700">{destination}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

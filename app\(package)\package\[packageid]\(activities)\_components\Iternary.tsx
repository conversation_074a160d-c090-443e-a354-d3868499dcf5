"use client";
import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import { useRouter } from "next/navigation";
import { MdCancel } from "react-icons/md";
import { <PERSON>Left, MapPin, Share2 } from "lucide-react";
import { ActivityEvent } from "@/app/types/pack";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
const Page = ({ events, day }: { events: ActivityEvent[]; day: number }) => {
  const router = useRouter();
  function navigateAddActivity(slot: number) {
    router.push(
      `activities/add?destination=${events[0]?.destinationId}&day=${day}&slot=${slot}`
    );
  }
  return (
    <div>
      <div className="flex w-full mr-[30px] lg:mr-[60px] overflow-x-auto flex-none p-4">
        {events?.length > 0 &&
          events?.map((event) =>
            event?.activityType === "allocated" ? (
              <div
                className="w-[160px] lg:w-[300px] p-4 lg:p-6"
                key={event?.activityId}
              >
                <div className="relative w-[150px] lg:w-[280px]">
                  <AlertDialog>
                    <AlertDialogTrigger>
                      <div
                        className="border border-[#FF5F5F] w-[145px] lg:w-[280px] h-[164px] lg:h-[320px] rounded-lg"
                        style={{
                          background:
                            "linear-gradient(0deg, #FFF 0%, #FFF 100%), rgba(255, 120, 101, 0.00)",
                          boxShadow:
                            "4px 4px 17.9px 0px rgba(101, 255, 190, 0.09)",
                        }}
                      >
                        <div className="w-full relative h-[95px] lg:h-[140px] flex items-center lg:mb-5">
                          <img
                            src={NEXT_PUBLIC_IMAGE_URL + event?.image}
                            className="z-0 w-full h-full rounded-tl-[7px] rounded-bl-[3px] rounded-tr-[7px]"
                            alt="image"
                          />
                          {/* Gradient overlays remain the same */}
                          <p
                            className="absolute top-0 bg-white p-[2px] lg:p-[4px] left-0 ml-[7px] lg:ml-[10px] mt-[7px] lg:mt-[10px] inline-flex items-center justify-center whitespace-nowrap w-auto h-auto rounded border text-white text-[8px] lg:text-[12px] font-semibold leading-normal tracking-[0.09px]"
                            style={{
                              textShadow: "1px 1px 1.6px rgba(0, 0, 0, 0.11)",
                              borderColor: "rgba(255, 255, 255, 0.33)",
                              backgroundColor: "rgba(249, 249, 249, 0.38)",
                              backdropFilter: "blur(7.599999904632568px)",
                            }}
                          >
                            {event?.timePeriod}
                          </p>
                        </div>
                        <span
                          className="ml-[10px] lg:ml-[15px] mt-5 lg:mt-4 font-Poppins text-[9px] lg:text-[16px] not-italic font-semibold leading-normal tracking-[0.09px] bg-clip-text text-transparent"
                          style={{
                            backgroundImage:
                              "linear-gradient(87.17deg, #FF5F5F -25.84%, #FF9080 118.31%)",
                          }}
                        >
                          {event?.name?.length > 20
                            ? event?.name?.slice(0, 21) + "..."
                            : event?.name}
                        </span>

                        <p className="ml-[10px] lg:ml-[15px] mt-1 lg:mt-2 mx-2 lg:mx-3 text-[7px] lg:text-[14px] text-[#6A778B] font-Poppins not-italic leading-normal tracking-[0.07px] font-medium">
                          {event?.description?.length >
                          (typeof window !== "undefined" &&
                          window.innerWidth >= 1024
                            ? 150
                            : 80)
                            ? event?.description?.slice(
                                0,
                                typeof window !== "undefined" &&
                                  window.innerWidth >= 1024
                                  ? 150
                                  : 80
                              ) + "..."
                            : event?.description}
                        </p>
                      </div>
                    </AlertDialogTrigger>
                    <AlertDialogContent className="w-4/5 lg:w-3/4">
                      <div
                        className="border border-[#FF5F5F] w-full h-auto rounded-lg"
                        style={{
                          background:
                            "linear-gradient(0deg, #FFF 0%, #FFF 100%), rgba(255, 120, 101, 0.00)",
                          boxShadow:
                            "4px 4px 17.9px 0px rgba(101, 255, 190, 0.09)",
                        }}
                      >
                        <div className="w-full relative h-[150px] lg:h-[300px] flex items-center">
                          <img
                            src={NEXT_PUBLIC_IMAGE_URL + event?.image}
                            className="z-0 w-full h-full rounded-tl-[7px] rounded-bl-[3px] rounded-tr-[7px]"
                            alt="image"
                            style={{ objectFit: "cover" }}
                          />
                          {/* Gradient overlays remain the same */}
                          <p
                            className="absolute top-0 bg-white left-0 ml-[7px] lg:ml-[15px] mt-[7px] lg:mt-[15px] inline-flex items-center justify-center p-1 lg:p-2 whitespace-nowrap w-auto h-auto rounded border text-white text-xs lg:text-base font-semibold leading-normal tracking-[0.09px]"
                            style={{
                              textShadow: "1px 1px 1.6px rgba(0, 0, 0, 0.11)",
                              borderColor: "rgba(255, 255, 255, 0.33)",
                              backgroundColor: "rgba(249, 249, 249, 0.38)",
                              backdropFilter: "blur(7.599999904632568px)",
                            }}
                          >
                            {event?.timePeriod}
                          </p>
                        </div>
                        <span
                          className="ml-3 lg:ml-6 pt-5 flex flex-wrap font-Poppins text-[15px] lg:text-[24px] not-italic font-semibold leading-normal tracking-[0.09px] bg-clip-text text-transparent"
                          style={{
                            backgroundImage:
                              "linear-gradient(87.17deg, #FF5F5F -25.84%, #FF9080 118.31%)",
                          }}
                        >
                          {event?.name}
                        </span>
                        <p className="ml-[10px] lg:ml-6 py-2 lg:py-4 h-auto flex flex-wrap mx-2 lg:mx-4 text-xs lg:text-base text-[#6A778B] font-Poppins not-italic leading-normal tracking-[0.07px] font-medium">
                          {event?.description}
                        </p>
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel className="rounded-lg border-[#FF5F5F] shadow-md text-sm lg:text-base">
                          Close
                        </AlertDialogCancel>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            ) : (
              <div
                onClick={() => navigateAddActivity(event?.slot)}
                key={event?.activityId}
                className=""
              >
                <div
                  className="w-[140px] lg:w-[280px] h-[160px] lg:h-[320px] mt-4 ml-[20px] lg:ml-[30px] flex flex-col items-center justify-center rounded-[10px] flex-shrink-0 cursor-pointer"
                  style={{
                    background:
                      "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
                    boxShadow: "4px 4px 17.9px 0px rgba(255, 120, 101, 0.09)",
                  }}
                >
                  <p className="text-white my-2 font-Poppins text-xs lg:text-base not-italic font-semibold leading-normal tracking-[0.12px]">
                    This slot is Free
                  </p>
                  <span className="text-white h-[10px] lg:h-[14px] lg:text-xl">
                    +
                  </span>
                </div>
              </div>
            )
          )}
      </div>
    </div>
  );
};

export default Page;

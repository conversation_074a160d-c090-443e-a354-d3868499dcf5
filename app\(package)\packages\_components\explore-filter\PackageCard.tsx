import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import Image from "next/image";
import React from "react";

interface PackageCardProps {
  packageName: string;
  imageUrl: string;
  numberOfNights: number;
  costPerPerson: number;
  startsFrom: string;
  hotelCount: number;
  cabCount: number;
  activityCount: number;
  numberOfDays : number;
}
const PackageCard: React.FC<PackageCardProps> = ({
  packageName,
  imageUrl,
  numberOfNights,
  costPerPerson,
  startsFrom,
  hotelCount,
  cabCount,
  activityCount,
}) => {
  return (
    <div className="sm:hidden lg:flex border p-2 rounded  flex-col items-start">
      <div className="flex items-start justify-between">
      <div className="relative h-32 w-32 mr-4">
          <Image src={NEXT_PUBLIC_IMAGE_URL + imageUrl} alt={packageName} layout="fill" objectFit="cover" className="rounded" />
        </div>

        <div className="flex flex-col">
            {packageName}
        </div>
        
      </div>

    </div>
  )
};

export default PackageCard;

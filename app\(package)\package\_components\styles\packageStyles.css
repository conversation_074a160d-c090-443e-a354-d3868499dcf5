.text {
  /* absolute drop-shadow-xl left-[16px]
     top-[175px] text-white text-[22px]
      font-medium leading-normal tracking-[0.22px]  */

  position: absolute;
  top: 175px;
  font-size: 22px;
  left: 16px;
  text-shadow: 2px 1px 10px black;
  color: white;
  font-weight: 800;
}
.textShade {
  text-shadow: 2px 1px 10px black;
  color: white;
  font-weight: 800;
}
.shadowTop {
  /* fixed bottom-0 left-0 right-0 bg-white  */

  color: #000;

  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: white;

  box-shadow: rgb(197, 196, 196) 0px -8px 25px -7px;
}

.shadowToplarge {
  color: #000;
  position: fixed;
  bottom: 0;
  left: 50%;
  width: 50%;
  background-color: white;
  box-shadow: rgb(197, 196, 196) 0px -8px 25px -7px;
}

@media (min-width: 1024px) {
  .shadowTop {
    color: #000;
    position: static;
    box-shadow: none; /* Remove shadow on large screens */
  }
}

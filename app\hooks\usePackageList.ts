import { useDispatch, useSelector } from "react-redux";
import { Theme, selectThemeId } from "../store/features/selectThemeSlice";
import { RootState } from "../store/store";
import { GetPackageQueryType, getPackages } from "../utils/api/getPackages";
import { useEffect, useState, useCallback } from "react";
import { PackageType } from "../types/package";
import { changeDate, changeDestinationId } from "../store/features/searchPackageSlice";
import { selectAdultsChild, selectPerRooom } from "../store/features/roomCapacitySlice";

export interface DateDestination {
  date: string;
  destination: string;
  destinationId: string;
}

export interface Room {
  perRoom: number;
  totalRooms: number;
  totalAdults: number;
  totalChilds: number;
  roomSchema: InputRoomWithId[];
}

interface InputRoomWithId {
  id: number;
  totalAdults: number;
  totalChilds: number;
}

export const usePackageList = (offset: number) => {
  // ,{destination,date,adult,child,totalRooms,perRoom,ts} :{destination:string,date:string,adult:string,child:string,totalRooms:string,perRoom:string,ts:string}
  const roomCapacityData: Room = useSelector(
    (store: any) => store.roomSelect.room
  );
  const themeSelected: Theme = useSelector(
    (state: RootState) => state.themeSelect
  );
  const dateAndDestination: DateDestination = useSelector(
    (store: any) => store.searchPackage
  );

  const [isLoading, setIsLoading] = useState(true);
  const [packageList, setPackageList] = useState<PackageType[]>([]);
  const [err, setErr] = useState<string>("");
  const [packageListHasNext, setPackageListHasNext] = useState(false);

  const fetchData = useCallback(
    async (payload: GetPackageQueryType) => {
      setIsLoading(true);
      try {
        const resp = await getPackages(payload);
        if (offset === 0) {
          setPackageList(resp.result.docs);
        } else {
          setPackageList((prevPackageList) => [
            ...prevPackageList,
            ...resp.result.docs,
          ]);
        }
        setPackageListHasNext(resp.result.hasNextPage);
      } catch (err: any) {
        setErr(err.message);
      } finally {
        setIsLoading(false);
      }
    },
    [offset]
  );

  useEffect(() => {
    
    // if (
    //   !dateAndDestination.destinationId || !themeSelected.themeId || !roomCapacityData.totalAdults
    // ) {
    //   dispatch(changeDestinationId(destination))
    //   dispatch(changeDate(date))
    //   dispatch(selectThemeId({selectedThemeId:ts}))
    //   dispatch(selectPerRooom(Number(perRoom)))
    //   dispatch(selectAdultsChild({adult:Number(adult),child:Number(child),room:Number(totalRooms)}))
    // }
    const extraAdult =
      roomCapacityData.totalAdults -
      roomCapacityData.totalRooms * roomCapacityData.perRoom;
    const noAdult = extraAdult > 0 ? roomCapacityData?.totalAdults-extraAdult : roomCapacityData?.totalAdults;

    const payload: GetPackageQueryType = {
      destinationId: dateAndDestination.destinationId,
      interestId: themeSelected.themeId,
      perRoom: roomCapacityData.perRoom,
      priceOrder: 1,
      startDate: dateAndDestination.date?.slice(0, 10),
      noAdult: noAdult,
      noChild: roomCapacityData.totalChilds,
      noRoomCount: roomCapacityData.totalRooms,
      noExtraAdult: extraAdult > 0 ? extraAdult : 0,
      offset: offset,
      limit: 10,
    };
    fetchData(payload);
  }, [offset]);

  return {
    packageList,
    isLoading,
    packageListHasNext,
    err,
  };
};

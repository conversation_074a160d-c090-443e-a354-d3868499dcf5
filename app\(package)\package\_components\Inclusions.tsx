/* eslint-disable @next/next/no-img-element */
"use client";
import React from "react";
import { CarFront } from "lucide-react";
import { CupSoda } from "lucide-react";
import { UtensilsCrossed } from "lucide-react";
import { <PERSON>Check } from "lucide-react";
import { PersonStanding } from "lucide-react";
import { Palmtree } from "lucide-react";
import { Inclusion } from "@/app/types/pack";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import Image from "next/image";
const Inclusions = ({ inclusions }: { inclusions: Inclusion[] }) => {
  return (
    <div className="mt-[50px] lg:px-5">
      <h1
        className=" font-Poppins text-[20px] lg:text-[30px] not-italic font-semibold leading-normal tracking-[0.18px] bg-clip-text text-transparent"
        style={{
          textShadow: "2px 4px 14.3px rgba(51, 214, 159, 0.15)",
          backgroundImage:
            "linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)",
        }}
      >
        Inclusions
      </h1>
      <h1 className="text-xs lg:text-sm mt-1 text-neutral-600">
        Your Package Includes
      </h1>
      <div className="mt-8 ml-[10px] flex flex-wrap  gap-3 lg:gap-5">
        {inclusions?.map((inc, index) => (
          <div
            key={index}
            className="rounded-[14px] w-[90px] lg:w-[120px] lg:h-[120px] lg:p-3 h-[90px] flex flex-col sm:flex-wrap p-1 sm:items-center sm:justify-center  shadow-md ring-1 ring-[#31DAA1] "
            style={{ background: "rgba(30, 192, 137, 0.05)" }}
          >
            <span className="flex items-center  justify-center w-[30px] h-[25px]">
              <img
                width={30}
                height={30}
                src={NEXT_PUBLIC_IMAGE_URL + inc?.image}
                alt={inc?.name}
                className="absolute ovject-cover h-[30px] w-[30px] lg:h-[45px] lg:w-[45px] lg:mb-3"
              />
            </span>
            <p className="pt-[5px] text-[#6A778B] font-Poppins text-[12px] lg:text-[16px] not-italic  text-center">
              {inc?.name}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Inclusions;

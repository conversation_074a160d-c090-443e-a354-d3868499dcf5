import { useCallback, useEffect, useState } from "react";
import { PackType } from "../types/pack";
import axios from "axios";
import { DateDestination, Room } from "./usePackageList";
import { useSelector } from "react-redux";
import { NEXT_PUBLIC_API_URL } from "../utils/constants/apiUrls";

export interface PackageGetQuery {
  packageId: string;
  startDate: string;
  noAdult: number;
  noChild: number;
  noRoomCount: number;
  noExtraAdult: number;
}
export const usePackage = (packageId: string) => {
  const [isLoading, setLoading] = useState(true);
  const [err, setErr] = useState("");
  const [pack, setPack] = useState<PackType>();

  const roomCapacityData: Room = useSelector(
    (store: any) => store.roomSelect.room
  );

  const dateAndDestination: DateDestination = useSelector(
    (store: any) => store.searchPackage
  );

  const fetchData = useCallback(async (payload: PackageGetQuery) => {
    setLoading(true);
    try {
      const resp: any = await axios.get(
        NEXT_PUBLIC_API_URL+"package/" + packageId + "/getOne",
        { params: payload }
      );
      setPack(resp.data?.result[0]);
    } catch (err: any) {
      setErr(err.message);
    } finally {
      setLoading(false);
    }
  }, []);
  console.log(roomCapacityData?.totalAdults,"room capacity data")
  useEffect(() => {
    const extraAdult =
      roomCapacityData.totalAdults -
      roomCapacityData.totalRooms * roomCapacityData.perRoom;
    const noAdult = extraAdult > 0 ? roomCapacityData?.totalAdults-extraAdult : roomCapacityData?.totalAdults;

    console.log(noAdult,"No Adult");
    const payload = {
      packageId: packageId,
      startDate: dateAndDestination?.date?.slice(0, 10),
      noAdult: noAdult,
      noChild: roomCapacityData?.totalChilds,
      noRoomCount: roomCapacityData?.totalRooms,
      noExtraAdult: extraAdult < 0 ? 0 : extraAdult,
    };
    fetchData(payload);
  }, []);
  return {
    pack,
    isLoading,
    err,
  };
};

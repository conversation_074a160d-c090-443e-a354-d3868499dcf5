"use client";
import React, { useEffect, useState } from "react";
import { CircleIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { <PERSON>Left, MapPin, Share2 } from "lucide-react";
import Iternary from "../_components/Iternary";
import { Activity } from "@/app/types/pack";
import PNF from "../_components/PNF";
import PackagesLoadingFull from "@/app/(user-area)/components/loading/PackagesLoadingFull";
import { useSelector } from "react-redux";
const Page = () => {
  const router = useRouter();
  const [error, setError] = useState(false);
  const packageData = useSelector((store: any) => store.package);
  console.log(packageData, "packageData");
  const clickBack = () => {
    router.back();
  };
  const [activity, setActivity] = useState<Activity[]>([]);
  const [isLoading, setLoading] = useState(true);
  const [packageName, setPackageName] = useState("");

  useEffect(() => {
    function validatePackage() {
      setLoading(true);
      const location = window.location.pathname;
      const packageId = location.split("/")[2];
      console.log(packageData.packageId, "pid");
      if (packageData.data.packageId === packageId) {
        setActivity(packageData.data.activity);
        setPackageName(packageData.data.packageName);
        setLoading(false);
      } else {
        setLoading(false);
        setError(true);
      }
    }
    const bool = validatePackage();
  }, [
    packageData.data.activity,
    packageData.data.packageId,
    packageData.data.packageName,
    packageData.packageId,
  ]);
  // if (error) {
  //   return <PNF />;
  // }
  console.log(activity, "activity");
  return isLoading ? (
    <PackagesLoadingFull />
  ) : (
    <div>
      <section className="">
        <div
          className="fixed top-0 text-center flex items-center justify-between  w-full h-auto py-5 lg:py-10 bg-white z-10"
          style={{ boxShadow: "0px 4px 36.1px 0px rgba(190, 190, 190, 0.22)" }}
        >
          <span className="pl-4 lg:pl-6 flex items-center">
            <button onClick={clickBack}>
              <ArrowLeft className="h-[30px] w-[30px] text-[#FF5F5F]" />
            </button>
          </span>
          <h1
            className="text-center flex flex-wrap px-2 h-auto font-Poppins text-[18px] lg:text-[24px] not-italic font-semibold leading-normal tracking-[0.18px]"
            style={{
              textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.20)",
              backgroundImage:
                "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
              backgroundClip: "text",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            {packageName}
          </h1>
          <span className="pr-4 lg:pr-6">
            <Share2 className="text-[#FF5F5F] w-[33px] " />
          </span>
        </div>
      </section>

      <div className="mt-[120px] ml-2 p-2">
        <h1
          className="  font-Poppins text-[18px] lg:text-[22px] mb-5 lg:mt-10 not-italic font-semibold leading-normal tracking-[0.18px]"
          style={{
            textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.20)",
            backgroundImage:
              "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          Itinerary Story
        </h1>
        {activity?.length > 0 &&
          activity?.map((data) => (
            <div key={data._id}>
              <div className="flex items-center justify-center">
                <span>
                  <CircleIcon className="w-[25px] h-[25px] rounded-full bg-[#FF7865] text-[#FF7865] " />
                </span>

                <h1 className="ml-8 font-semibold text-[15px] lg:text-[20px] w-full">
                  Day {data?.day} &nbsp;{" "}
                  <span className="text-[#FF7865]">{data?.fullStartDate}</span>{" "}
                  {data?.from && `- ${data?.from} to ${data?.to}`}
                </h1>
              </div>

              <div className="flex">
                <span
                  className="ml-[10px]"
                  style={{ borderLeft: "4px dashed #FF9080" }}
                ></span>

                <div className="overflow-x-auto ">
                  <Iternary events={data?.event} day={data?.day} />
                </div>
              </div>
            </div>
          ))}
        <div className="text-[#FF7865] font-bold mb-4">End of Trip</div>
      </div>
    </div>
  );
};

export default Page;

import toast from 'react-hot-toast';
import api from '../api/auth';
import { setAccessToken, setRefreshToken } from '../constants/accessToken';

export const verifyGoogleAccessToken = async (token: string) => {
  try {
    const response = await api.put('auth/google/oauth', {
      token: token,
    });
    if (response.data?.result?.accessToken) {
      setAccessToken(response.data?.result?.accessToken);
      setRefreshToken(response.data?.result?.refreshToken);
    }

    return Promise.resolve(response);
  } catch (error) {
    console.log('Google OAUTH Error', error);
    toast.error('An error occurred ');
    return Promise.reject();
  }
};

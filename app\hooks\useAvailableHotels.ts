import { useCallback, useEffect, useState } from "react";
import { NEXT_PUBLIC_API_URL } from "../utils/constants/apiUrls";
import axios from "axios";
import { useSelector } from "react-redux";
import { DateDestination, Room } from "./usePackageList";
import { HotelRoom } from "../types/hotel";
import { isDateWithinRanges } from "../(package)/package/[packageid]/hotelchange/roomchange/_components/checkDates";
export interface HotelQuery {
  noOfNight: number;
  startDate: string;
  noOfChild: number;
  noRoomCount: number;
  noExtraAdult: number;
}
export const useAvailableHotels = (
  packageId: string,
  destinationId: string,
) => {
  // const prevHotelYStartDate = useSelector(
  //   (store: any) => store.hotelChange?.replaceHotel?.yStartDate
  // );
  // const prevHotelYEndDate = useSelector(
  //   (store: any) => store.hotelChange?.replaceHotel?.yEndDate
  // )
  const roomCapacityData: Room = useSelector(
    (store: any) => store.roomSelect.room
  ); 
  const dateAndDestination: DateDestination = useSelector(
    (store: any) => store.searchPackage
  );
  const prevHotel = useSelector(
    (store: any) => store.hotelChange?.replaceHotel
  );
  const [hotel, setHotel] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [err, setErr] = useState("");

  //package/PLMANHO-87JX-6D5N2A/available/get
  const fetchData = useCallback(async (payload: HotelQuery) => {
    setLoading(true);
    try {
      const resp = await axios.get(
        NEXT_PUBLIC_API_URL + `package/${packageId}/available/get`,
        { params: payload }
      );
      const filteredHotels = resp?.data?.result[0]?.hotelDetail.filter(
        (item: any) => item?.destinationId === destinationId
      );
      
      // const updatedHotels = filteredHotels?.map((hotel: any) => ({
      //   ...hotel,
      //   hotelRoom: hotel.hotelRoom?.map((room: HotelRoom) => ({
      //     ...room,
      //     mealPlan: room.mealPlan.filter((mp) =>
      //       isDateWithinRanges(
      //         mp.startDate,
      //         mp.endDate,
      //         prevHotelYStartDate,
      //         prevHotelYEndDate
      //       )
      //     ),
      //   })),
      // }));
      // setHotel(updatedHotels);

      setHotel(filteredHotels);
      
    } catch (err: any) {
      setErr(err.message);
    } finally {
      setLoading(false);
    }
  }, []);
  useEffect(() => {
    const extraAdult =
      roomCapacityData.totalAdults -
      roomCapacityData.totalRooms * roomCapacityData.perRoom;
    const payload = {
      noOfNight: prevHotel?.noOfNight,
      startDate: dateAndDestination.date?.slice(0, 10),
      noOfChild: roomCapacityData.totalChilds,
      noRoomCount: roomCapacityData.totalRooms,
      noExtraAdult: extraAdult > 0 ? extraAdult : 0,
    };
    fetchData(payload);
  }, []);

  return { hotel, isLoading, err };
};

'use client';
import { verifyGoogleAccessToken } from '@/app/utils/api/verifyGoogleSignin';
import { getToken } from 'next-auth/jwt';
import { signIn, useSession } from 'next-auth/react';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import PackagesLoading from '@/app/(user-area)/components/loading/PackagesLoading';
import PackagesLoadingFull from '@/app/(user-area)/components/loading/PackagesLoadingFull';

const GoogleSignIn = () => {
  const router = useRouter();

  const { data } = useSession();

  useEffect(() => {
    const verifyAccessToken = async () => {
      if (data?.accessToken) {
        try {
          await verifyGoogleAccessToken(data.accessToken);
          const url = localStorage.getItem('currentPath') || '/';
          localStorage.removeItem('currentPath');
          router.push(url);
        } catch (error) {
          console.error('Error verifying access token:', error);
          // Handle any error during verification
        }
      }
    };

    if (data?.accessToken) {
      verifyAccessToken();
    }
  }, [data?.accessToken, router]);
  return (
    <button
      className='flex  mb-6 gap-3 items-center justify-center     mt-4 p-2 border border-neutral-200 cursor-pointer  rounded-md'
      onClick={() => signIn('google')}
    >
      <span className='relative w-8 h-8 '>
        <Image
          fill
          className='object-cover'
          alt='Google'
          src={
            'https://cdn1.iconfinder.com/data/icons/google-s-logo/150/Google_Icons-09-512.png'
          }
        />
      </span>
      Continue with Google
    </button>
  );
};

export default GoogleSignIn;

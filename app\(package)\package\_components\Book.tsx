"use client";
import React, { useState } from "react";
import {
  Drawer,
  Drawer<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import FareBreakup from "./FareBreakup";
import { ChevronDown } from "lucide-react";
import { FaIndianRupeeSign } from "react-icons/fa6";
import { useAppSelector } from "@/app/store/store";
import { useRouter } from "next/navigation";
import {
  formatIndianCurrency,
  formatIndianNumber,
  roundOffPrice,
} from "@/lib/utils";
import { useDispatch } from "react-redux";
import { setUser } from "@/app/store/features/userSlice";
import { useAuth } from "@/app/hooks/useAuth";

export default function Book({
  packageId,
  price,
  adult,
  child,
  packagePrice,
  gstPrice,
  gstPer,
}: {
  packageId: string;
  price: number;
  adult: number;
  child: number;
  packagePrice: number;
  gstPrice: number;
  gstPer: number;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [showFareBreakup, setShowFareBreakup] = useState(false);
  const actualPrice = roundOffPrice(price + (20 / 100) * price);
  const dispatch = useDispatch();
  const { isAuthenticated, user, isLoading } = useAuth();
  const router = useRouter();

  async function handlePackageClick() {
    if (!isAuthenticated && !isLoading) {
      localStorage.setItem(
        "currentPath",
        `/package/${packageId}/booking-overview`
      );
      router.push("/sign-in");
    } else {
      if (!isLoading && isAuthenticated) {
        dispatch(setUser(user));
        router.push(`${packageId}/booking-overview`);
      }
    }
  }

  const FareBreakupContent = () => (
    <div className="bg-[#AFAFAF12] rounded-lg p-6 shadow-lg max-w-sm">
      <h2 className="text-xl text-[#FF5F5F] mb-6 flex justify-center">
        Fare Breakup
      </h2>

      <div className="space-y-6">
        {/* Basic Cost */}
        <div className="border-b pb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-700 font-medium">Total Basic Cost</span>
            <span className="text-gray-700 font-medium">
              {formatIndianCurrency(packagePrice)}
            </span>
          </div>
          <div className="flex justify-between text-sm text-gray-500">
            <span>
              {adult} Adults + {child} Child
            </span>
            <span> {formatIndianCurrency(packagePrice)}</span>
          </div>
        </div>

        {/* Taxes */}
        <div className="border-b pb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-700 font-medium">Fees & Taxes</span>
            <span className="text-gray-700 font-medium">
              {" "}
              {formatIndianCurrency(gstPrice)}
            </span>
          </div>
          <div className="flex justify-between text-sm text-gray-500">
            <span>GST {gstPer}%</span>
            <span> {formatIndianCurrency(gstPrice)}</span>
          </div>
        </div>

        {/* Total */}
        <div className="pt-2">
          <div className="flex justify-between items-end">
            <span className="text-gray-700 font-semibold text-lg">Total</span>
            <div className="text-right">
              <div className="text-gray-700 font-semibold text-2xl">
                {/* <PriceDisplay price={price} actualPrice={actualPrice} /> */}
                {formatIndianCurrency(price)}
              </div>
              <div className="text-[#FF5F5F] text-xs">Package Cost</div>
            </div>
          </div>
        </div>
        <div className=" flex flex-col items-center justify-center space-y-4">
          <BookButton onClick={handlePackageClick} />
        </div>
      </div>
    </div>
  );

  return (
    <div className="shadowTop lg:w-full lg:max-w-5xl fixed bottom-0 right-0 lg:p-8">
      {/* Mobile View */}
      <div className="lg:hidden px-5">
        <div className="flex justify-center">
          <hr className="w-[244px] h-[4px] rounded-[22px] bg-bookLineBg text-center" />
        </div>

        <div className="flex justify-between items-center my-5">
          <div className="flex flex-col gap-1">
            <Drawer>
              <DrawerTrigger>
                <div>
                  <button className="flex items-center font-semibold bg-neutral-100 rounded-lg text-[10px] whitespace-nowrap gap-1 px-2 py-1 border text-[#5D6670]">
                    Fare Details
                    <ChevronDown size={14} className="stroke-2" />
                  </button>
                </div>
              </DrawerTrigger>

              <DrawerContent>
                <DrawerHeader>
                  <DrawerTitle>
                    <FareBreakup
                      adults={adult}
                      child={child}
                      packagePrice={packagePrice}
                      gstPer={gstPer}
                      gstPrice={gstPrice}
                    />
                  </DrawerTitle>
                </DrawerHeader>
                <DrawerFooter>
                  <DrawerClose />
                </DrawerFooter>
              </DrawerContent>
            </Drawer>
            <PriceDisplay price={price} actualPrice={actualPrice} />
          </div>

          <BookButton onClick={handlePackageClick} />
        </div>
      </div>

      {/* Desktop View */}
      <div className="hidden lg:inline ">
        <div className="">
          <FareBreakupContent />
        </div>
        <div className=" flex flex-col items-center justify-center space-y-4"></div>
      </div>
    </div>
  );
}

// Extracted components for cleaner code
const PriceDisplay = ({
  price,
  actualPrice,
}: {
  price: number;
  actualPrice: number;
}) => (
  <>
    <div className="text-xs lg:text-sm text-neutral-700 line-through flex items-center justify-center decoration-[#FF5F5F] mb-[-5px] ml-[2px] font-medium">
      <FaIndianRupeeSign
        size={10}
        className="line-through decoration-[#FF5F5F]"
      />
      {formatIndianNumber(actualPrice)}
    </div>
    <div className="flex items-center gap-1 lg:justify-center">
      <h1 className="text-2xl lg:text-3xl font-semibold flex items-center text-[#5D6670]">
        <FaIndianRupeeSign size={14} className="lg:text-xl" />{" "}
        {formatIndianNumber(price)}
      </h1>
      <h1 className="text-[#FF5F5F] small-text lg:text-base whitespace-nowrap font-semibold">
        Total Cost
      </h1>
    </div>
  </>
);

const BookButton = ({ onClick }: { onClick: () => void }) => (
  <button
    onClick={onClick}
    className="w-[120px] lg:w-[160px] h-[40px] lg:h-[48px] bg-bookNowBg shadow-bookNowShadow rounded-[30px] text-white text-[14px] lg:text-[16px] font-semibold leading-normal tracking-[0.14px]"
  >
    Book Now
  </button>
);

"use client";
import React, { useEffect, useState } from "react";
import PackageDetail from "../_components/PackageDetail";
import { fetchPackage } from "@/app/store/features/packageSlice";
import PackagesLoading from "@/app/(user-area)/components/loading/PackagesLoading";
import { PackType } from "@/app/types/pack";
import PNF from "./(activities)/_components/PNF";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { DateDestination, Room } from "@/app/hooks/usePackageList";
import { PackageGetQuery } from "@/app/hooks/usePackage";
import { AppDispatch } from "@/app/store/store";

export default function PackageDetails({ params }: any) {
  const useAppDispatch = () => useDispatch<AppDispatch>();
  const [error, setError] = useState(false);
  const dispatch = useAppDispatch();

  const roomCapacityData: Room = useSelector(
    (store: any) => store.roomSelect.room
  );

  const dateAndDestination: DateDestination = useSelector(
    (store: any) => store.searchPackage
  );
  const pack = useSelector((store: any) => store.package);
  const { packageid } = params;
  useEffect(() => {
    if (
      !pack?.isActivityUpdated &&
      !pack?.isHotelUpdated &&
      !pack?.isVehicleUpdated
    ) {
      const extraAdult =
        roomCapacityData.totalAdults -
        roomCapacityData.totalRooms * roomCapacityData.perRoom;
      const payload: PackageGetQuery = {
        packageId: packageid,
        startDate: dateAndDestination?.date?.slice(0, 10),
        noAdult:
          extraAdult > 0
            ? roomCapacityData?.totalAdults - extraAdult
            : roomCapacityData?.totalAdults,
        noChild: roomCapacityData?.totalChilds,
        noRoomCount: roomCapacityData?.totalRooms,
        noExtraAdult: extraAdult < 0 ? 0 : extraAdult,
      };
      dispatch(fetchPackage(payload));
    }
    if (
      pack?.data?.packageId !== packageid &&
      pack?.data?.packageId !== undefined
    ) {
      setError(true);
    }
  }, []);
  // if(pack.error || error || packageid === undefined){
  //   return <PNF></PNF>
  // }

  return pack.isLoading ? (
    <div className="w-full min-h-[100vh] flex justify-center items-center">
      <PackagesLoading />
    </div>
  ) : (
    <div>
      <PackageDetail pack={pack.data as PackType} />
    </div>
  );
}

// https://api.tripxplo.com/v1/api/user/package/booking

import api from './auth';
export  const getAllBookings = async (offset:number, limit:number) => {
    
    try {
        const response = await api.get(`package/booking`, {params: {offset, limit}});
        return Promise.resolve(response?.data);
      } catch (error) {
        console.log(error);
        return Promise.reject('Invalid Coupon');
      }
}
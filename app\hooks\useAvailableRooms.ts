import { useCallback, useEffect, useState } from "react";
import { NEXT_PUBLIC_API_URL } from "../utils/constants/apiUrls";
import axios from "axios";
import { useSelector } from "react-redux";
import { HotelRoom } from "../types/hotel";
import { isDateWithinRanges } from "../(package)/package/[packageid]/hotelchange/roomchange/_components/checkDates";
import { DateDestination, Room } from "./usePackageList";

export const useAvailableRooms = (
) => {
  const prevHotel = useSelector(
    (store: any) => store.hotelChange?.replaceHotel
  );
  const roomCapacityData: Room = useSelector(
    (store: any) => store.roomSelect.room
  ); 
  const dateAndDestination: DateDestination = useSelector(
    (store: any) => store.searchPackage
  );
  const [rooms, setRooms] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [err, setErr] = useState("");
  const fetchData = useCallback(async (payload:any) => {
    setLoading(true);
    try {
        // package/4935fd54-a9a8-4417-a93f-c57661d3b48e/hotel/get
        // startDate ,noOfNight, noOfChild, noRoomCount, noExtraAdult
      const resp = await axios.get(
        NEXT_PUBLIC_API_URL + `package/${prevHotel.hotelId}/hotel/get`,{params:payload});

      //   const filterRooms = resp?.data?.result?.map((room: HotelRoom) => ({
      //     ...room,
      //     mealPlan: room.mealPlan.filter((mp) =>
      //         isDateWithinRanges(
      //             mp.startDate,
      //             mp.endDate,
      //             prevHotel?.yStartDate,
      //             prevHotel?.yEndDate
      //         )
      //     ),
      // }));
      // setRooms(filterRooms);
      setRooms(resp?.data?.result);
    } catch (err: any) {
      setErr(err.message);
    } finally {
      setLoading(false);
    }
  }, []);
  useEffect(() => {
    const extraAdult =
    roomCapacityData.totalAdults -
    roomCapacityData.totalRooms * roomCapacityData.perRoom;
  const payload = {
    noOfNight: prevHotel.noOfNight,
    startDate: dateAndDestination.date?.slice(0, 10),
    noOfChild: roomCapacityData.totalChilds,
    noRoomCount: roomCapacityData.totalRooms,
    noExtraAdult: extraAdult > 0 ? extraAdult : 0,
  };
    fetchData(payload);
  }, []);

  return { rooms, isLoading, err };
};

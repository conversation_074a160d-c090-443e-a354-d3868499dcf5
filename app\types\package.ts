
export interface PackageType{
    _id:string,
    AgentAmount:number,
    activity:Activity[],
    activityCount:number,
    agentCommissionPer:number,
    destination:Destination[]
    finalPackagePrice:number,
    gstPer:number,
    gstPrice:number,
    hotel:Hotel[],
    hotelCount:number,
    hotelMeal:HotelMeal[],
    isMark:boolean,
    marketingPer:number,
    noOfDays:number,
    noOfNight:number,
    packageId:string,
    packageImg:string[],
    packageName:string,
    perPerson:number,
    planName:string,
    startFrom:string,
    totalActivityPrice:number,
    totalAdditionalFee:number,
    totalCalculationPrice:number,
    totalPackagePrice:number,
    totalRoomPrice:number,
    totalTransportFee:number,
    vehicleCount:number,
    vehicle:Vehicle[]
    totalVehiclePrice:number
    }
    
    interface Activity{
        day:number,
    event:ActivityEvent[],
    from:string,
    startDateWise:number,
    to:string,
    _id:string
    }
   export  interface ActivityEvent{
    
    activityId:string,
    activityType:string,
    destinationId:string,
    image:string,
    name:string,
    slot:number,
    timePeriod:string,
    __v:number,
    _id:string
    }
   export interface Destination{
        destinationId:string,
    destinationName:string,
    destinationType:string,
    noOfNight:number,
    rankNo:number,
    __v:number,
    _id:string,
    }
   export  interface HotelMeal{
        adultPrice:number,
        childPrice:number,
        endDate:string[],
        endDateWise:number,
        gstAdultPrice:number,
        gstChildPrice:number,
        gstExtraAdultPrice:number,
        gstPer:number,
        hotelId:string,
        hotelMealId:string,
        hotelRoomId:string,
        hotelRoomType:string,
        isAc:boolean,
        isAddOn:boolean,
        maxAdult:number,
        maxChild:number,
        maxInf:number,
        mealPlan:string,
        noOfNight:number,
        roomCapacity:number,
        roomPrice:number,
        seasonType:string,
        sort:number,
        startDate:string[],
        startDateWise:number,
        totalAdultPrice:number,
        totalChildPrice:number,
        totalExtraAdultPrice:number,
        __v:number,
        _id:string,
    }

    export interface Hotel{
        _id: string,
        hotelId: string,
        hotelName: string,
        image: string,
        location: {
            destinationId: string,
            lat: string,
            address: string,
            state: string,
            country: string,
            _id: string
        },
        viewPoint: string[],
        contract: {
            businessEmail: string,
            additionalEmail: string,
            maintainerPhoneNo: number,
            _id: string
        },
        amenities: string[],
        __v: number,
        review: number
    }

    export interface Vehicle{
        _id: string,
        vehicleId: string,
        vehicleName: string,
        image: string,
        isAc: boolean,
        luggage: number,
        seater: number,
        maxPax: number,
        vehicleCompany: string,
        acType: string,
        itineraryName: string[],
        transferInfo: string[],
        inclusion: string[],
        noOfDays: number,
        price: number,
        destinationId: string,
        createdAt: string,
        updatedAt: string,
        __v: number
    }
    
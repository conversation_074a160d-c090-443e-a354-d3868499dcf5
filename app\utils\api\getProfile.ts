// http://localhost:8000/v1/api/user/profile

import { UserProfile } from '@/app/types';
import api from './auth';
import toast from 'react-hot-toast';
export const getProfile = async () => {
  try {
    const response = await api.get('profile');

    return Promise.resolve(response?.data);
  } catch (error) {
    return Promise.reject('error');
  }
};

export const updateProfile = async (values: UserProfile) => {
  try {
    const response = await api.put('profile/update', values);
    return Promise.resolve(response?.data);
  } catch (error) {
    return Promise.reject('error');
  }
};

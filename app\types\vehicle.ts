export interface VehicleDetail {
  _id: string;
  vehicleId: string;
  vehicleName: string;
  image: string;
  isAc: boolean;
  luggage: number;
  seater: number;
  maxPax: number;
  vehicleCompany: string;
  acType: "Private" | string;
  itineraryName: string[];
  transferInfo: string[];
  inclusion: string[];
  noOfDays: number;
  price: number;
  destinationId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export type VehicleDetails = VehicleDetail[];

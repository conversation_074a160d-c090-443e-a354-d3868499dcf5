import axios from "axios";
import { NEXT_PUBLIC_API_URL } from "../constants/apiUrls";

export interface GetPackageQueryType{
    destinationId?:string,
    interestId?:string,
    planId?:string,
    perRoom?:number,
    priceOrder?:number,
    startDate?:string,
    noAdult?:number,
    noChild?:number,
    noRoomCount?:number,
    noExtraAdult?:number,
    offset?:number,
    limit?:number
}
export const getPackages = async  (payload:GetPackageQueryType)=>{
    try {
        const response = await axios.get(NEXT_PUBLIC_API_URL+"package" as string,{params:payload});
        return response.data;
    } catch (error) {
        console.log(error);
    }
}
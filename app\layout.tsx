import type { Metadata } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import { ReduxProvider } from "./store/provider";
import TanStackProvider from "./providers/ReactQueryProvider";
import { ToastProvider } from "./providers/Toaster";
import Head from "next/head";
import { Providers } from "./providers/SessionProvider";
import { GoogleAnalytics, GoogleTagManager } from "@next/third-parties/google";
import Image from "next/image";

const poppins = Poppins({
  subsets: ["latin-ext"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title:
    "Customized Tour Packages | Domestic & International Holidays | TripXplo",
  description:
    "Plan your dream vacation with TripXplo. Explore customized tour packages for domestic and international destinations. Easy booking and best rates guaranteed!",
  keywords:
    "Customized tour packages, domestic holidays, international holidays, travel packages, vacation planning",
  openGraph: {
    url: "https://tripxplo.com/",
    title:
      "Customized Tour Packages | Domestic & International Holidays | TripXplo",
    description:
      "Plan your dream vacation with TripXplo. Explore customized tour packages for domestic and international destinations. Easy booking and best rates guaranteed!",
    // images: [
    //   {
    //     url: "https://tripxplo.com/logo/tripxplo-logo.png",
    //     alt: "TripXplo Logo",
    //   },
    // ],
    // type: "website",
  },
  // icons: {
  //   icon: "/logo/tripxplo-favicon.ico",
  //   shortcut: "/logo/tripxplo-favicon.ico",
  // },
  robots: "index, follow",
  viewport: "width=device-width, initial-scale=1.0, maximum-scale=5.0",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <body className={poppins.className}>
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-M8SQMRKQ"
            height="0"
            width="0"
            style={{ display: "none", visibility: "hidden" }}
            title="GTM"
          />
        </noscript>
        <Providers>
          <div className="">
            <TanStackProvider>
              <ReduxProvider>
                <ToastProvider />

                {children}
              </ReduxProvider>
            </TanStackProvider>
          </div>
          {/* <div className="hidden lg:flex items-center justify-between h-screen px-10 mt-[-20px] relative">
            <div className="text-left   border-gray-300 rounded-lg z-10 w-1/2">
              <p className="text-3xl font-semibold text-gray-600">
                We&apos;re crafting the Desktop version
                <br />
                <span className="text-muted-foreground text-[20px] font-normal tracking-wide">
                  Please visit{" "}
                  <span className="text-[#FF5F5F] font-semibold ">
                    tripxplo.com
                  </span>{" "}
                  with your <span className="font-semibold">Mobile Phones</span>
                </span>
              </p>
            </div>
            <div className="relative w-3/5 h-full">
              <Image
                src="/building.svg"
                alt="Background"
                fill
                className="absolute object-contain"
              />
            </div>
          </div> */}
        </Providers>
      </body>
      <GoogleAnalytics gaId="G-THRVS4Q1EQ" />
      <GoogleTagManager gtmId="GTM-M8SQMRKQ" />
    </html>
  );
}

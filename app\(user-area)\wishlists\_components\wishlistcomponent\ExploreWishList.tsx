"use client";
import React, { useState } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import Wishlist from "./Wishlist";
import MyIternary from "./MyIternary";
import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";

const ExploreWishList = () => {
  const router = useRouter();

  const clickBack = () => {
    router.push("/");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 navbar-spacing">
      {/* Mobile Header */}
      <div className="fixed lg:hidden flex top-0 text-center items-center w-full h-[100px] bg-white/95 backdrop-blur-sm z-20 shadow-lg border-b border-gray-100">
        <span className="pl-[40px] flex items-center">
          <button
            onClick={clickBack}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
          >
            <ArrowLeft className="h-[28px] w-[28px] text-[#FF5F5F]" />
          </button>
        </span>
        <h1 className="text-center ml-[16px] font-Poppins text-[20px] font-bold leading-normal tracking-[0.18px] bg-gradient-to-r from-[#FF5F5F] to-[#FF9080] bg-clip-text text-transparent">
          Wishlists
        </h1>
      </div>

      {/* Desktop Header */}
      <div className="hidden lg:block pt-8 pb-6">
        <div className="max-w-6xl mx-auto px-6">
          <div className="flex items-center gap-4 mb-2">
            <button
              onClick={clickBack}
              className="p-3 rounded-full hover:bg-white/80 transition-all duration-200 shadow-md hover:shadow-lg border border-gray-200"
            >
              <ArrowLeft className="h-6 w-6 text-[#FF5F5F]" />
            </button>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-[#FF5F5F] to-[#FF9080] bg-clip-text text-transparent">
              My Wishlists
            </h1>
          </div>
          <p className="text-gray-600 ml-16">Manage your saved trips and itineraries</p>
        </div>
      </div>

      <div className="pt-[120px] lg:pt-0 flex justify-center px-4">
        <div className="w-full max-w-4xl">
          <Tabs defaultValue="myIternary" className="w-full">
            {/* Enhanced Tab Navigation */}
            <div className="flex justify-center mb-8">
              <div className="bg-white/80 backdrop-blur-sm rounded-full p-1 shadow-lg border border-gray-200">
                <TabsList className="bg-transparent border-0 rounded-full">
                  <TabsTrigger
                    value="myIternary"
                    className="rounded-full px-6 py-3 text-sm font-medium transition-all duration-200 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#FF5F5F] data-[state=active]:to-[#FF9080] data-[state=active]:text-white data-[state=active]:shadow-md hover:bg-gray-100"
                  >
                    My Itinerary
                  </TabsTrigger>
                  <TabsTrigger
                    value="wishlist"
                    className="rounded-full px-6 py-3 text-sm font-medium transition-all duration-200 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#FF5F5F] data-[state=active]:to-[#FF9080] data-[state=active]:text-white data-[state=active]:shadow-md hover:bg-gray-100"
                  >
                    Wishlist
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>

            {/* Tab Content */}
            <div className="min-h-[400px]">
              <TabsContent value="myIternary" className="mt-0">
                <MyIternary />
              </TabsContent>
              <TabsContent value="wishlist" className="mt-0">
                <Wishlist />
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default ExploreWishList;

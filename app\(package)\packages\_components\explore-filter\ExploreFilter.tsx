"use client";
import FilterCards from "./FilterCards";
import React, { useEffect, useState } from "react";
import FilterCategoryList from "./FilterCategoryList";
import { FILTER_CATEGORIES } from "@/app/utils/constants/filterCategories";
import { useAppSelector } from "@/app/store/store";
import { PackageType } from "@/app/types/package";

const ExploreFilter = (props: { allPackages: PackageType[] }) => {
  const destination = useAppSelector(
    (state) => state.searchPackage.destination
  );

  return (
    <div className="sm:block ">
      <div className="lg:hidden mt-6 flex flex-col justify-center pl-3">
        <header className=" text-[#1EC089] text-left  font-semibold sm:text-[20px] lg:text-[24px]  ">
          List of Packages
        </header>
        <h1 className="text-neutral-600 sm:text-xs lg:text-sm sm:mt-0 lg:mt-1 ">
          Various Packages available for{" "}
          <span className=" text-neutral-600 font-medium text-sm">
            {destination}
          </span>
        </h1>
      </div>

      <div>
        <div className="sm:flex lg:flex-wrap gap-[10px] lg:mt-10 cursor-default overflow-x-auto whitespace-nowrap p-2">
          {FILTER_CATEGORIES.map((category, index) => (
            <FilterCategoryList key={index} label={category.label} />
          ))}
        </div>
      </div>
      <div></div>
    </div>
  );
};

export default ExploreFilter;

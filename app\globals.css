@import url("https://fonts.googleapis.com/css2?family=Mukta:wght@500&family=Poppins:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Mukta:wght@500&family=Poppins:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  font-family: "Poppins", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}

#app {
  height: 100%;
}

.carousel-container {
  scroll-snap-type: x mandatory;
  overflow-x: scroll;
  display: flex;
  padding-left: 10px;
  padding-right: 10px;
}
.small-text {
  font-size: 10px;
  line-height: 1.2;
  align-self: flex-end;
  margin-bottom: 7px;
}
.truncate-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
}
.truncate-1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.truncate-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 3;
}

.truncate-1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.green-t-shadow {
  text-shadow: 3px 3px 6px rgba(64, 251, 154, 0.3);
}
.red-t-shadow {
  text-shadow: 3px 3px 10px rgba(255, 95, 95, 0.285);
}
.white-shadow {
  box-shadow: -3px -3px 20px rgb(255, 255, 255);
}
.grey-shadow {
  box-shadow: 0px 15px 25px rgba(76, 76, 76, 0.171);
}

.text-h {
  @apply text-neutral-700;
}

.carousel {
  scroll-snap-align: start;
  flex-shrink: 0;
}
body {
  font-family: "Poppins";
}
*::-webkit-scrollbar {
  display: none;
}

/* Navbar spacing utilities */
.navbar-spacing {
  @apply lg:pt-20;
}

.navbar-spacing-large {
  @apply lg:pt-24;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.text-stroke {
  -webkit-text-stroke-width: 0.10000000149011612;
  -webkit-text-stroke-color: var(--Grey, #6a778b);
}

@keyframes moveDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  50% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(10px);
  }
}

.dot {
  animation: moveDown 1s infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

.dot:nth-child(4) {
  animation-delay: 0.6s;
}

.form-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.form-group label {
  position: absolute;
  top: 0.5rem;
  left: 1rem;
  transition: top 0.3s, font-size 0.3s;
  font-size: 0.75rem;
  color: #666;
  pointer-events: none;
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label {
  top: -1rem;
  font-size: 0.625rem;
  color: #999;
}

.form-group input {
  width: 100%;
  border-radius: 0.25rem;
  padding: 0.8rem;
  font-size: 1rem;
  border: 1px solid #ccc;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #ff5f5f;
}

.form-group .error-message {
  color: #ff5f5f;
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

.input-field {
  position: relative;
  width: 100%;
  padding: 10px;
  border: #ff5f5f;
  font-size: 16px;
  background-color: white; /* Ensure background color */
}

.input-label {
  position: absolute;
  top: 50%;
  left: 10px;
  font-size: 16px;
  background-color: white;
  padding: 0 2px 0 2px;
  color: grey;
  transform: translateY(-100%);
  transition: all 0.3s ease;
  pointer-events: none;
}

.input-field:focus + .input-label,
.input-field:not(:placeholder-shown) + .input-label {
  top: -1px;
  left: 10px;
  font-size: 12px;
  border: #ff5f5f;
  color: #ff7865;
}

.input-wrapper {
  position: relative;
  margin: 1em 0;
}

@keyframes wave {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 238, 136, 0.7);
  }

  50% {
    box-shadow: 0 0 0 30px rgba(66, 219, 135, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66, 219, 135, 0);
  }
}
.wave {
  animation: wave 1.7s infinite;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

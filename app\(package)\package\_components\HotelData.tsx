/* eslint-disable @next/next/no-img-element */
"use client";
import { HotelMeal } from "@/app/types/pack";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import {
  ChevronLeftCircle,
  ChevronUp,
  MoveRight,
  MoveUp,
  Pencil,
} from "lucide-react";
import { GiRoundStar } from "react-icons/gi";
import { CalendarDays, Soup } from "lucide-react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { setReplaceHotel } from "@/app/store/features/hotelChangeSlice";
import { useState } from "react";

export default function HotelData({
  hotel,
  index,
}: {
  hotel: HotelMeal;
  index: number;
}) {
  const router = useRouter();
  const dispatch = useDispatch();
  const packId = useSelector((store: any) => store.package.data?.packageId);

  function changeRoom() {
    dispatch(setReplaceHotel(hotel));
    router.push(`${packId}/roomchange`);
  }

  const [showAll, setShowAll] = useState(false);

  const handlevp = () => {
    setShowAll(!showAll);
  };

  function changeHotel() {
    dispatch(setReplaceHotel(hotel));
    router.push(`${packId}/hotelchange`);
  }

  const mealPlans = {
    cp: "Breakfast Included",
    map: "Breakfast & Dinner",
    ap: "All Meals Included",
    ep: "Rooms Only",
  };

  return (
    <div className="mt-[48px]">
      <div className="flex justify-between  ">
        <p
          className="h-[29px] w-[80px] text-[18px] font-semibold bg-clip-text text-transparent leading-normal tracking-[0.18px]"
          style={{
            backgroundImage:
              "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
            textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.13)",
          }}
        >
          Hotel - {index}
        </p>

        {/* Change Hotel Functionality below */}

        <button
          onClick={changeHotel}
          className="flex gap-1 justify-center leading-normal tracking-[0.18px] mb-2 items-center px-2 py-2 whitespace-nowrap bg-gradient-to-r cursor-pointer from-[#8391A1] to-[#5D6670] rounded-[14px] text-white text-[10px] not-italic font-semibold"
        >
          <Pencil size={12} /> <h1 className="cursor-pointer">Change Hotel</h1>
        </button>
      </div>
      <div
        className="flex flex-col w-full rounded-[14px] border-2 border-[#FF5F5F] p-2 mt-2 bg-white"
        style={{ boxShadow: "4px 8px 25.8px 0px rgba(254, 76, 86, 0.13)" }}
      >
        <div className="lg:flex lg:flex-row lg:space-x-[24px]">
          <div className="relative h-[150px] w-full lg:w-1/2 rounded-lg">
            <img
              src={NEXT_PUBLIC_IMAGE_URL + hotel?.image}
              width={100}
              height={150}
              alt={hotel?.hotelName}
              className="absolute w-full h-full object-cover rounded-md"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-black w-24 opacity-80 via-transparent to-transparent rounded-md" />
            <div className="absolute inset-0 bg-gradient-to-tl from-black  opacity-30 via-transparent to-transparent rounded-md" />

            <p
              className="ml-[7px] mt-[7px] inline-flex items-center justify-center w-[57px] h-[19px] rounded border text-white text-[10px] font-semibold leading-normal tracking-[0.09px]"
              style={{
                textShadow: "1px 1px 1.6px rgba(0, 0, 0, 0.11)",
                borderColor: "rgba(255, 255, 255, 0.33)",
                backgroundColor: "rgba(249, 249, 249, 0.38)",
                backdropFilter: "blur(7.599999904632568px)",
                zIndex: 1,
              }}
            >
              {hotel?.noOfNight} Nights
            </p>
            <p
              className="absolute bottom-2 right-3 flex items-center gap-1 text-white text-[8px] font-semibold z-10"
              style={{
                textShadow: "2px 1px 4px rgba(0, 0, 0, 0.34)",
                zIndex: 1,
              }}
            >
              <GiRoundStar size={12} className="text-[#FFD230]" />
              <span className="textShade text-[10px]">
                {hotel?.contract?.additionalEmail} / 5
              </span>
            </p>
          </div>

          <div>
            <div className=" flex flex-col items-start justify-between mt-[10px] ">
              <p
                className="  text-[18px]  font-semibold leading-6  tracking-[0.2px] bg-clip-text text-transparent truncate-3"
                style={{
                  backgroundImage:
                    "linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)",
                }}
              >
                {hotel?.hotelName}
              </p>
              <div className="flex flex-col h-[42px] justify-between mt-4">
                <p className="flex items-center whitespace-nowrap text-neutral-600 text-[12px] font-normal leading-normal">
                  <CalendarDays
                    className="text-[#FF7865] mr-[4px] drop-shadow-pkgdoneShadow"
                    size={15}
                  />
                  {hotel?.fullStartDate} - {hotel?.fullEndDate}
                </p>
                <p className="flex items-center whitespace-nowrap text-neutral-600 text-[12px] font-normal leading-normal">
                  <Soup
                    className="text-[#FF7865] mr-[4px] drop-shadow-pkgdoneShadow"
                    size={15}
                  />{" "}
                  {mealPlans[hotel?.mealPlan]}
                </p>
              </div>
            </div>

            <div className="flex items-center flex-wrap mt-2 ">
              {hotel?.viewPoint
                ?.slice(0, showAll ? hotel.viewPoint.length : 2)
                .map((vp, i) => (
                  <p
                    key={vp}
                    className=" mt-[12px] inline-flex whitespace-nowrap items-center h-[14px] px-2 py-2 rounded-[3.5px] border-[#FF7865] border-[0.817px] text-[#FF7865] text-[10px] font-normal leading-[11.667px]"
                    style={{
                      boxShadow:
                        "1.167px 2.333px 8.342px 0px rgba(255, 120, 101, 0.20)",
                    }}
                  >
                    {vp}
                  </p>
                ))}
              {hotel?.viewPoint?.length > 2 && (
                <button
                  onClick={handlevp}
                  className="ml-[7px] mt-[12px] text-[#FF7865] text-[10px] decoration-[#FF7865] underline flex-nowrap"
                >
                  {showAll ? (
                    <ChevronLeftCircle
                      className="  fill-[#FF7865] text-white "
                      size={18}
                    />
                  ) : (
                    "other viewpoints"
                  )}
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="border-t border-neutral-200 py-1 w-full flex items-center justify-start whitespace-nowrap mt-[14px] space-x-2" />
        <div className="lg:flex lg:flex-row justify-between">
          <div className="flex flex-row items-center ">
            <div className="text-[#6A778B] text-[12px] pl-[4px] truncate decoration-[#FF9080] flex items-center gap-1 ">
              <h1 className="text-[10px] text-neutral-600">Room Type</h1>
              <span className="text-[14px] text-[#FF7865] font-medium mt-[-2px] w-44 truncate">
                {hotel?.hotelRoomType}
              </span>
            </div>
          </div>

          <div className="py-2">
            <button
              onClick={changeRoom}
              className="text-[10px] font-medium flex gap-2 rounded-md items-center bg-[#D9D9D9] p-2 bg-opacity-30 text-[#5D6670]"
            >
              Change room <MoveRight size={10} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";
import React, { useEffect, useState } from "react";
import "../../../../../globals.css";

import {
  School,
  CalendarDays,
  Soup,
  ArrowRight,
  Check,
  Star,
} from "lucide-react";
import { FaIndianRupeeSign, FaLocationDot } from "react-icons/fa6";
import Image from "next/image";
import { HotelMeal } from "@/app/types/pack";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import {
  HotelChangeDataType,
  HotelMealType,
  HotelRoom,
} from "@/app/types/hotel";
import { useDispatch } from "react-redux";
import {
  changeHotel,
  changeHotelAndCalculatePrice,
} from "@/app/store/features/packageSlice";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { setHotelData } from "@/app/store/features/hotelChangeSlice";
import { AppDispatch } from "@/app/store/store";
import { GiRoundStar } from "react-icons/gi";

const FilterCardList = ({
  hotel,
  startEndDate,
  prevHotel,
}: {
  hotel: HotelChangeDataType;
  startEndDate: string;
  prevHotel: HotelMeal;
}) => {
  const useAppDispatch = () => useDispatch<AppDispatch>();
  const dispatch = useAppDispatch();
  const [selectedRoom, setSelectedRoom] = useState<HotelRoom>();
  const [selectedMealPlan, setSelectedMealPlan] = useState<HotelMealType>();
  const [mealPlanPrice, setMealPlanPrice] = useState(0);
  const mealPlans = {
    cp: "Breakfast Included",
    map: "Breakfast & Dinner",
    ap: "All Meals Included",
    ep: "Rooms Only",
    CP: "Breakfast Included",
    MAP: "Breakfast & Dinner",
    AP: "All Meals Included",
    EP: "Rooms Only",
  };
  const router = useRouter();
  function selectHotel() {
    /**
        mealPlan: HotelMealType;
        hotelRoom: HotelRoom;
        hotel: HotelChangeDataType;
        prevHotel: HotelMeal;
    */
    dispatch(
      changeHotelAndCalculatePrice({
        mealPlan: selectedMealPlan as HotelMealType,
        hotelRoom: selectedRoom as HotelRoom,
        hotel: hotel,
        prevHotel: prevHotel,
      })
    );
    router.back();
  }
  function changeRoom() {
    dispatch(setHotelData(hotel));

    router.push("hotelchange/roomchange");
  }
  const [reject, setReject] = useState(false);
  useEffect(() => {
    const selectedRoom = hotel.hotelRoom?.find(
      (data) => data?.mealPlan?.length > 0
    );
    setSelectedRoom(selectedRoom);
    if (selectedRoom === undefined) {
      setReject(true);
      return;
    }
    const selectedmp = selectedRoom?.mealPlan.find((data) =>
      prevHotel?.mealPlan?.includes(data.mealPlan)
    );
    setSelectedMealPlan(selectedmp);
    if (selectedmp === undefined) {
      setReject(true);
      return;
    }
    /**
     *   "totalAdultPrice": 10500,
          "gstAdultPrice": 0,
          "totalChildPrice": 100,
          "gstChildPrice": 0,
          "totalExtraAdultPrice": 0,
          "gstExtraAdultPrice": 0
     */
    const prevPrice =
      prevHotel?.totalAdultPrice +
      prevHotel?.gstAdultPrice +
      prevHotel?.totalChildPrice +
      prevHotel?.gstChildPrice +
      prevHotel?.gstExtraAdultPrice +
      prevHotel?.totalExtraAdultPrice;

    const currentMeal = selectedmp;
    let currentPrice =
      currentMeal?.totalAdultPrice +
      currentMeal?.gstAdultPrice +
      currentMeal?.totalChildPrice +
      currentMeal?.gstChildPrice +
      currentMeal?.gstExtraAdultPrice +
      currentMeal?.totalExtraAdultPrice;

    // currentPrice = prevHotel.noOfNight * currentPrice;
    setMealPlanPrice(currentPrice - prevPrice);
  }, [hotel]);

  if (reject) return;
  console.log("hotels", hotel);
  return (
    <div className=" flex flex-col  h-auto w-full lg:w-[410px] lg:h-[352px] rounded-[14px] bg-white shadow-changeHotelBoxShadow border-[#4BCDA1] border-[2px] p-[8px]  my-2">
      <div className="h-[112px] w-full rounded-[8px] relative bg-gray-700 shadow-changeHotelImgShadow bg-changeRoomImgBg ">
        {/* {prevHotel.hotelId === hotel?.hotelId &&
          prevHotel.hotelRoomId === selectedRoom?.hotelRoomId &&
          prevHotel.mealPlan === selectedMealPlan?.mealPlan && (
            <div className="absolute z-50 top-[9px]  left-2 bg-green-500 flex px-3 py-1 rounded-lg items-center text-white text-[8px] font-semibold ">
              Selected
            </div>
          )} */}

        <img
          src={NEXT_PUBLIC_IMAGE_URL + hotel?.image}
          alt={hotel?.hotelName}
          className="absolute z-0 w-full h-full object-cover rounded-lg"
        />
        <p
          className="inline-flex items-center ml-[11px] mt-[11px] text-white text-[9px] font-normal leading-normal tracking-[0.09px] "
          style={{ textShadow: "1px 1px 1.6px rgba(0, 0, 0, 0.11)" }}
        >
          <FaLocationDot size={9} className="text-white mr-[4px]" />
          {hotel?.location?.state}
        </p>
        <p
          className="absolute bottom-[9px] right-[14px] flex items-center text-white text-[8px] font-semibold leading-[11.667px] tracking-[1px] "
          style={{ textShadow: "2px 1px 4px rgba(0, 0, 0, 0.34)" }}
        >
          <GiRoundStar size={10} className="text-[#FFD230] mr-[4px]" />
          {hotel?.contract?.additionalEmail} / 5
        </p>
        {prevHotel.hotelId === hotel?.hotelId &&
          prevHotel.hotelRoomId === selectedRoom?.hotelRoomId &&
          prevHotel.mealPlan === selectedMealPlan?.mealPlan && (
            <div className="absolute top-[9px]  left-2 bg-green-500 flex px-3 py-1 rounded-lg items-center text-white text-[9px] font-semibold ">
              Selected
            </div>
          )}
      </div>

      <div>
        <div className="flex items-center lg:h-[68px]  justify-between gap-3  p-2">
          <p className="flex flex-wrap text-[#6A778B] text-[16px] font-semibold ">
            {hotel?.hotelName}
          </p>
          <p
            className="   whitespace-nowrap items-center h-auto px-1 rounded-[3.5px] border-[#FF7865] border-[0.817px] text-[#FF7865] text-[10px] font-normal   "
            style={{
              boxShadow:
                "1.167px 2.333px 8.342px 0px rgba(255, 120, 101, 0.20)",
            }}
          >
            {hotel?.viewPoint[0]}
          </p>
        </div>
        <div className="flex justify-between lg:justify-center lg:h-[93px] py-3 border-b-[1px] border-changeHotelLine w-full px-2">
          <div className="flex   gap-2  ">
            <div>
              <Image src="/Room.svg" width={16} height={16} alt="room" />
            </div>
            <div className="flex flex-col  ">
              <p className=" flex   text-[#4BCDA1]  text-[12px] whitespace-nowrap  ">
                Room Type :
              </p>
              <p className=" lg:w-[200px] text-[12px] whitespace-wrap font-semibold truncate-2  text-[#6A778B]  ">
                {selectedRoom?.hotelRoomType && selectedRoom.hotelRoomType}
              </p>
            </div>
            {/* <p className=" pl-7 text-[12px] whitespace-wrap font-semibold truncate-2  text-[#6A778B] font-normal  ">
              {selectedRoom?.hotelRoomType && selectedRoom.hotelRoomType}
            </p> */}
          </div>
          <div className="flex flex-col h-auto gap-2 justify-between lg:justify-center">
            <p className=" flex items-center text-[#6A778B] font-medium text-[10px]  leading-normal">
              <CalendarDays
                className="text-[#FF7865] mr-[4px] drop-shadow-pkgdoneShadow"
                size={15}
              />{" "}
              {startEndDate}
            </p>
            <p className=" flex items-center text-[#6A778B] text-[10px] font-medium leading-normal">
              <Soup
                className="text-[#FF7865] mr-[4px] drop-shadow-pkgdoneShadow"
                size={15}
              />{" "}
              {selectedMealPlan?.mealPlan &&
                mealPlans[selectedMealPlan.mealPlan]}
            </p>
          </div>
        </div>
        <div className="flex items-center h-auto py-2 w-full justify-between">
          <button onClick={changeRoom}>
            <p className="  flex items-center px-[5px] py-[5px] rounded-[5px] border text-[#6A778B] text-[10px] whitespace-nowrap font-normal leading-normal tracking-[0.08px] ">
              Change Room{" "}
              <ArrowRight size={8} className="text-[#6A778B] ml-[4px]" />{" "}
            </p>
          </button>

          <div className="flex items-center">
            {mealPlanPrice > 0 && (
              <Image
                src="/Plus.svg"
                height={7}
                width={7}
                alt="plus"
                className="mr-[3px] "
              />
            )}
            <Image
              src="/Rupees.svg"
              height={7}
              width={7}
              alt="rupees"
              className="mr-[3px]"
            />
            <p
              className=" font-montserrat text-[14px] font-semibold leading-[16.2px] tracking-[0.36px] bg-clip-text text-transparent "
              style={{
                backgroundImage:
                  " linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
              }}
            >
              {Math.ceil(mealPlanPrice)}
            </p>
            {!(
              prevHotel.hotelId === hotel?.hotelId &&
              prevHotel.hotelRoomId === selectedRoom?.hotelRoomId &&
              prevHotel.mealPlan === selectedMealPlan?.mealPlan
            ) && (
              <button onClick={selectHotel}>
                <p className="ml-[20px] flex items-center px-4 shadow-md cursor-pointer py-2 rounded-lg border border-[#27B182] text-[#27B182] text-[12px] font-medium leading-normal tracking-[0.1px] ">
                  Select
                </p>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterCardList;

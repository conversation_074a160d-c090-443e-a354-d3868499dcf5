"use client"
import React from 'react'
import PackageCardList from './PackageCardList';
import { useAppSelector } from '@/app/store/store';

interface Package {
    name : string,
    days : number,
    nights : number
    price : number,
    description : string,
    image_url : string
    pkg_category : string
}

const packageCardList: Package[] = [
    {
      name: 'Manali Budget',
      days: 2,
      nights: 3,
      price: 456,
      description: 'lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      image_url: '',
      pkg_category: 'Trending',
    },
    {
      name: 'Goa Getaway',
      days: 4,
      nights: 5,
      price: 789,
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      image_url: '',
      pkg_category: 'Festival',
    },
    {
      name: 'Himalayan Adventure',
      days: 7,
      nights: 8,
      price: 1200,
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      image_url: '',
      pkg_category: 'EMI',
    },
    {
      name: 'Beach Paradise',
      days: 3,
      nights: 4,
      price: 600,
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      image_url: '',
      pkg_category: 'Offers',
    },
    {
      name: 'City Explorer',
      days: 5,
      nights: 6,
      price: 900,
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      image_url: '',
      pkg_category: 'Holiday',
    },
    {
      name: 'Mountain Retreat',
      days: 6,
      nights: 7,
      price: 1000,
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      image_url: '',
      pkg_category: 'Industrial Visit',
    },
    {
      name: 'Historical Journey',
      days: 3,
      nights: 4,
      price: 550,
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      image_url: '',
      pkg_category: 'Workation',
    },
    {
      name: 'Island Escape',
      days: 5,
      nights: 6,
      price: 850,
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      image_url: '',
      pkg_category: 'Trending',
    },
    {
      name: 'Wildlife Safari',
      days: 4,
      nights: 5,
      price: 700,
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      image_url: '',
      pkg_category: 'Festival',
    },
    {
      name: 'Desert Adventure',
      days: 3,
      nights: 4,
      price: 500,
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      image_url: '',
      pkg_category: 'EMI',
    },
  ];
  
  

const PackageCards = () => {
    const packageCategory = useAppSelector((state)=>state.pkgCategory.pkgCategory)
    const packageData= packageCardList.filter(
        (pkg) => pkg.pkg_category === packageCategory
      );
    

  return (
    <div className='carousel-container gap-2 '>
        {
            packageData.map((pkg,index)=>(
               
                <PackageCardList 
                name={pkg.name}
                days = {pkg.days}
                nights={pkg.nights}
                price={pkg.price}
                description={pkg.description}
                image_url={pkg.image_url}
                pkg_category={pkg.pkg_category}
                key={index}
                />
            ))
        }

    </div>
  )
}

export default PackageCards
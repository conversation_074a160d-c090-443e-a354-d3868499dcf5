"use client";
import React, { useEffect } from "react";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { changeSearchInterest } from "@/app/store/features/searchInterestSlice";
import Image from "next/image";

import {setPkgCategory} from '@/app/store/features/pkgCategorySlice'
import { useAppSelector } from "@/app/store/store";
import { cn } from "@/lib/utils";

const PackageCategory = () => {
  const dispatch = useDispatch();
  const {pkgCategory} =useAppSelector((state)=>state.pkgCategory)

  const [packageData , setPackageData] = useState([])
  const [pack, setPack] = useState("Trending");

  


  async function fetchData() {
    const data = await fetch(
      "https://api.tripxplo.com/v1/api/user/package/category/get"
    );
    const json = await data.json();
    console.log(json.result);
    setPackageData(json.result);
  }

  useEffect(() => {
    fetchData();
  }, []);

  const handleCategoryClick=(categoryName : string)=>{
    dispatch(setPkgCategory(categoryName))
  }

  return (
    <div className="flex py-3 space-x-5  overflow-x-auto px-2">
      {
        packageData.map((cat : any)=>(
          <div key={cat.id}>
            <div onClick={()=>handleCategoryClick(cat.categoryName)}className={cn("cursor-pointer flex items-center  space-x-3 whitespace-nowrap rounded-lg px-3 py-2 border transition shadow-sm hover: text-[#1EC089]  hover:bg-neutral-50 " ,cat.categoryName === pkgCategory && " ring-1 ring-[#1EC089] text-[#1EC089] shadow-sm  ")}>
           <div className="relative w-4 h-4">
           <Image fill className="object-cover" alt={cat.categoryName} src={`https://tripemilestone.in-maa-1.linodeobjects.com/${cat.image}`} />
           </div>
           <h1 className="text-[14px] cursor-pointer">
           {cat.categoryName}
           </h1>
            </div>

          </div>
        ))
      }

    </div>
  
  );
};

export default PackageCategory;

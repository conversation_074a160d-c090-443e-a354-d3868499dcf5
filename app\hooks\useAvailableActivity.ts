import { useCallback, useEffect, useState } from "react";
import { NEXT_PUBLIC_API_URL } from "../utils/constants/apiUrls";
import axios from "axios";
import { useSelector } from "react-redux";
import { DateDestination, Room } from "./usePackageList";
export interface ActivityQuery {
  noOfNight: number;
  startDate: string;
  noOfChild: number;
  noRoomCount: number;
  noExtraAdult: number;
}
export const useAvailableActivity = (packageId: string,destinationId:string) => {
  const roomCapacityData: Room = useSelector(
    (store: any) => store.roomSelect.room
  );

  const dateAndDestination: DateDestination = useSelector(
    (store: any) => store.searchPackage
  );
  const [activity, setActivity] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [err, setErr] = useState("");

  //package/PLMANHO-092M-6D5N2A/activity/get
  const fetchData = useCallback(async (payload: ActivityQuery) => {
    setLoading(true);
    try {
      const resp = await axios.get(
        NEXT_PUBLIC_API_URL + `package/${packageId}/activity/get`,
        { params: payload }
      );
      setActivity(resp?.data?.result[0]?.activityDetails.filter((item:any)=>item?.destinationId === destinationId));
    } catch (err: any) {
      setErr(err.message);
    } finally {
      setLoading(false);
    }
  }, []);
  useEffect(() => {
    const extraAdult =
      roomCapacityData.totalAdults -
      roomCapacityData.totalRooms * roomCapacityData.perRoom;
    const payload = {
      noOfNight: 1,
      startDate: dateAndDestination.date?.slice(0, 10),
      noOfChild: roomCapacityData.totalChilds,
      noRoomCount: roomCapacityData.totalRooms,
      noExtraAdult: extraAdult > 0 ? extraAdult : 0,
    };
    fetchData(payload);
  }, []);

  return { activity, isLoading, err };
};

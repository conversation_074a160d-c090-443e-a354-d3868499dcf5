import React from "react";
import { X } from "lucide-react";
import { AiFillCloseSquare } from "react-icons/ai";
import { Exclusion } from "@/app/types/pack";
const Exclusions = ({ exclusions }: { exclusions: Exclusion[] }) => {
  return (
    <div className="mt-[35px] w-[309px] lg:w-full lg:px-5">
      <h1
        className=" text-transparent   bg-clip-text  font-Poppins text-[20px] lg:text-[30px] not-italic font-semibold leading-normal tracking-[0.18px]"
        style={{
          textShadow: " 2px 4px 14.3px rgba(255, 120, 101, 0.24)",
          backgroundImage:
            "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
        }}
      >
        Exclusion
      </h1>
      <h1 className="text-xs lg:text-sm mt-1 text-neutral-600">
        What&apos;s Not in the Package
      </h1>

      <div className=" mt-8 lg:ml-[25px]">
        {exclusions?.map((exc, i) => (
          <div className=" flex items-center gap-2 mt-6" key={exc.name}>
            <span>
              <AiFillCloseSquare size={18} className="text-[#FF5F5F] " />
            </span>
            <p className="pl-[10px] font-Poppins text-neutral-600 lg:text-[#FF5F5F] text-[14px]  not-italic font-thin ">
              {exc?.name}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Exclusions;

import { Bell, BellDotIcon } from "lucide-react";
import Image from "next/image";
import React from "react";

const Header = () => {
  return (
    <div className="px-5 lg:hidden flex justify-between items-center pt-5">
      <div>
        <Image
          src="/logo.png" // Make sure to add your logo image in the public/images folder
          alt="Tripxplo Logo"
          width={120} // Adjust the width as needed
          height={40} // Adjust the height as needed
          priority
        />
      </div>
      <Bell className="text-slate-500" />
    </div>
  );
};

export default Header;

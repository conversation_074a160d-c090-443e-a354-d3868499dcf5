/* eslint-disable @next/next/no-img-element */
"use client";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import { ArrowLeft, ChevronLeft, Share2 } from "lucide-react";
import Image from "next/image";
import "./styles/packageStyles.css";
import { useRouter } from "next/navigation";

export default function PackageImage({
  img,
  name,
}: {
  img: string;
  name: string;
}) {
  const router = useRouter();

  return (
    <div className="relative w-full bg-cover h-[293px] lg:h-[120px] bg-slate-600 lg:bg-white">
      <img
        src={`${NEXT_PUBLIC_IMAGE_URL}${img}`}
        width={100}
        height={100}
        alt="image"
        className="w-full h-full object-cover lg:hidden"
      />
      <div className="flex">
        <div
          className="p-[5px] border absolute top-6 left-6 border-neutral-100 shadow-sm bg-white rounded-md flex justify-center cursor-pointer"
          onClick={() => router.push("/packages")}
        >
          <ChevronLeft size={24} className="text-[#FF5F5F]" />
        </div>
        <div className="hidden lg:inline absolute top-6 left-[70px]">
          <header className="font-Poppins sm:text-[20px] lg:text-[24px] font-semibold text-[#FF5F5F] drop-shadow-black drop-shadow-sm">
            Top Packages
          </header>
        </div>
      </div>

      <p className="absolute top-6 right-5 flex  justify-center items-center  p-3 rounded-full bg-white">
        {" "}
        <Share2 size={16} className="text-[#FF5F5F]" />{" "}
      </p>
      <div className="lg:hidden absolute inset-x-0 bottom-6 h-56 bg-gradient-to-t from-[#151617] to-transparent "></div>
      <p className="absolute left-5 bottom-16 drop-shadow-2xl lg:mb-10 lg:text-3xl  decoration-black  brightness-125 text-white font-semibold text-xl">
        {name}
      </p>
    </div>
  );
}

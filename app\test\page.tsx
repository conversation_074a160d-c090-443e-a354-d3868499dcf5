'use client';
import React, { useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useRouter } from 'next/navigation';

const App = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, isError } = useAuth();

  useEffect(() => {
    if (isLoading) {
      // Optionally handle loading state, e.g., show a spinner
      return;
    }

    if (isError || !isAuthenticated) {
      router.push('/register');
    }
  }, [isLoading, isError, isAuthenticated, router]);

  if (isLoading) {
    return <div>Loading...</div>; // Show loading state if necessary
  }

  if (isError) {
    return <div>Error loading user information</div>; // Handle errors
  }

  return <div>App</div>;
};

export default App;

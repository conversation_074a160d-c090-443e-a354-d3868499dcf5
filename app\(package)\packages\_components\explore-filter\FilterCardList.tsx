"use client";
import {
  Building,
  CarFront,
  IndianRupee,
  MapPin,
  Clock,
  Users,
  Plane,
  Utensils,
  Heart,
} from "lucide-react";
import { LuDices } from "react-icons/lu";
import Image from "next/image";
import { PackageType } from "@/app/types/package";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import { cn, formatIndianNumber } from "@/lib/utils";

import { useMemo, useState } from "react";
import { HotelMeal, Vehicle } from "@/app/types/pack";

// Helper function to get meal plan description
const getMealPlanDescription = (code: string) => {
  if (typeof code !== "string") {
    return "N/A";
  }
  switch (code.toUpperCase()) {
    case "EP":
      return "Room Only";
    case "CP":
      return "Breakfast Included";
    case "MAP":
      return "Breakfast and Dinner Included";
    case "AP":
      return "All Meals Included";
    default:
      return code;
  }
};

const FilterCardList = (props: { package: PackageType }) => {
  console.log("Vehicle data:", props.package.vehicle);
  const {
    packageImg,
    packageName,
    planName,
    destination,
    noOfDays,
    noOfNight,
    vehicle,
    hotel,
    hotelCount,
    perPerson,
    hotelMeal,
  } = props.package;

  const [isWishlisted, setIsWishlisted] = useState(false);

  const uniqueMealPlans = useMemo(() => {
    if (!hotelMeal || hotelMeal.length === 0) {
      return [];
    }

    const mealPlanCounts = hotelMeal.reduce((acc, meal) => {
      const description = getMealPlanDescription(meal.mealPlan);
      if (description !== "N/A") {
        acc[description] = (acc[description] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const plans = Object.keys(mealPlanCounts);

    if (
      plans.includes("Breakfast Included") &&
      plans.includes("Breakfast and Dinner Included")
    ) {
      if (
        mealPlanCounts["Breakfast and Dinner Included"] >=
        mealPlanCounts["Breakfast Included"]
      ) {
        return plans.filter((plan) => plan !== "Breakfast Included");
      } else {
        return plans.filter((plan) => plan !== "Breakfast and Dinner Included");
      }
    }

    return plans;
  }, [hotelMeal]);

  const mealPlanDisplay =
    uniqueMealPlans.length > 1
      ? uniqueMealPlans.filter((plan) => plan !== "N/A").join(", ")
      : uniqueMealPlans.join(", ");

  const amenities = useMemo(() => {
    const allAmenities = new Set<string>();
    hotel?.forEach((h) => h.amenities.forEach((a) => allAmenities.add(a)));
    return Array.from(allAmenities)
      .sort(() => 0.5 - Math.random())
      .slice(0, 3);
  }, [hotel]);

  const handleWishlistClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
    // Add wishlist logic here
  };

  const price = perPerson;

  return (
    <>
      {/* Desktop View - Hidden on mobile */}
      <div
        className={cn(
          "hidden lg:flex bg-white rounded-2xl shadow-md overflow-hidden cursor-pointer transition-all duration-500 w-full max-w-sm mx-auto flex-col mb-6 border border-gray-200/60 backdrop-blur-sm",
          "hover:shadow-xl hover:-translate-y-3 hover:scale-[1.02] hover:border-gray-300/80",
          "transform-gpu will-change-transform",
          props.package.planName === "Gold"
            ? "border-[#EF831E]/60 hover:border-[#EF831E] hover:shadow-[0_8px_30px_rgba(239,131,30,0.15)] hover:ring-2 hover:ring-[#EF831E]/20"
            : props.package.planName === "Silver"
            ? "border-[#95A1AF]/60 hover:border-[#95A1AF] hover:shadow-[0_8px_30px_rgba(149,161,175,0.15)] hover:ring-2 hover:ring-[#95A1AF]/20"
            : props.package.planName === "Platinum"
            ? "border-[#CA0B0B]/60 hover:border-[#CA0B0B] hover:shadow-[0_8px_30px_rgba(202,11,11,0.15)] hover:ring-2 hover:ring-[#CA0B0B]/20"
            : "hover:border-green-500/80 hover:shadow-[0_8px_30px_rgba(34,197,94,0.15)] hover:ring-2 hover:ring-green-500/20"
        )}
      >
        <div className="relative w-full h-52 overflow-hidden">
          <Image
            src={NEXT_PUBLIC_IMAGE_URL + packageImg[0]}
            alt={packageName}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-500"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
          <div className={cn(
              "absolute top-3 left-3 flex justify-center shadow-lg px-3 py-1.5 text-xs font-semibold text-white rounded-full backdrop-blur-sm",
              "border border-white/20 transition-all duration-300 hover:scale-105",
              props.package.planName === "Gold"
                ? "bg-gradient-to-r from-[#EF831E] to-[#F59E0B] shadow-[#EF831E]/30"
                : props.package.planName === "Silver"
                ? "bg-gradient-to-r from-[#95A1AF] to-[#6B7280] shadow-[#95A1AF]/30"
                : props.package.planName === "Platinum"
                ? "bg-gradient-to-r from-[#CA0B0B] to-[#DC2626] shadow-[#CA0B0B]/30"
                : "bg-gradient-to-r from-[#1EC089] to-[#10B981] shadow-[#1EC089]/30"
            )}
          >
            {planName}
          </div>
        </div>
        <div className="p-5 flex flex-col flex-grow">
          <h3 className="text-[#1EC089] text-xl font-semibold leading-tight mb-3 line-clamp-2 hover:text-[#16A085] transition-colors duration-300">
            {packageName}
          </h3>
          <div className="space-y-2.5 mb-4">
            <div className="flex items-center text-gray-700">
              <div className="flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mr-3">
                <MapPin size={16} className="text-app-primary" />
              </div>
              <span className="text-sm font-medium">
                  {destination?.filter(d => d.noOfNight > 0).map((dest, i, arr) => (
                      <span key={i} className="inline-flex items-center">
                        <span className="text-[#FF7865] font-semibold text-sm">
                          {dest.noOfNight}N
                        </span>
                        <span className="text-gray-700 font-medium text-sm ml-1">
                          - {dest.destinationName}
                        </span>
                        {i < arr.length - 1 && (
                          <span className="text-gray-400 mx-2 font-normal"> • </span>
                        )}
                      </span>
                  ))}
              </span>
            </div>

          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center text-gray-700">
              <div className="flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mr-3">
                <Clock size={16} className="text-app-primary" />
              </div>
              <span className="text-sm font-medium">
                {noOfNight} Nights, {noOfDays} Days
              </span>
            </div>
            <div className="text-xs bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-full px-3 py-1.5 flex items-center space-x-1">
              <span className="text-gray-600 font-medium">Starts</span>
              <span className="text-gray-500">@</span>
              <span className="text-[#1EC089] font-semibold">
                {props.package.startFrom}
              </span>
            </div>
          </div>
        </div>
          <div className="border-t border-gray-100 pt-4 mb-4">
            <p className="font-semibold text-gray-800 mb-3 text-base">What's Included:</p>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
                <div className="w-4 h-4 rounded-full flex items-center justify-center">
                  <Plane size={16} className="text-app-primary" />
                </div>
                <span className="text-gray-700 font-medium">Airport Transfer</span>
              </div>
              <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
                <div className="w-4 h-4 rounded-full flex items-center justify-center">
                  <Building size={16} className="text-app-primary" />
                </div>
                <span className="text-gray-700 font-medium">{hotelCount} Hotels</span>
              </div>
              <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
                <div className="w-4 h-4 rounded-full flex items-center justify-center">
                  <Utensils size={16} className="text-app-primary" />
                </div>
                <span className="text-gray-700 font-medium">{mealPlanDisplay}</span>
              </div>
              <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
                <div className="w-4 h-4 rounded-full flex items-center justify-center">
                  <CarFront size={16} className="text-app-primary" />
                </div>
                <span className="text-gray-700 font-medium">
                  {vehicle && vehicle.length > 0 ? `${vehicle[0].seater} Seater` : "Cab"}
                </span>
              </div>
            </div>
          </div>

          <div className="flex-grow"></div>

          <div className="flex justify-between items-center pt-4 border-t border-gray-100">
            <div className="flex flex-col">
              <div className="flex items-center">
                <IndianRupee size={20} className="text-app-primary mr-1" />
                <span className="text-2xl font-bold text-app-primary">
                  {formatIndianNumber(perPerson)}
                </span>
              </div>
              <p className="text-xs text-gray-500 ml-6">per person</p>
            </div>
            <button
              className="bg-gradient-to-r from-app-secondary to-emerald-600 text-white font-semibold px-4 py-2.5 text-sm rounded-xl hover:from-emerald-600 hover:to-app-secondary transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 hover:-translate-y-0.5"
            >
              View Details
            </button>
          </div>
        </div>
      </div>

      {/* Mobile View - Hidden on desktop */}
      <div
        className={cn(
          "flex lg:hidden cursor-pointer items-start w-full mt-3 mb-8 h-[9rem] overflow-auto rounded-md shadow-sm ring-1",
          props.package.planName === "Gold"
            ? "ring-[#EF831E]"
            : props.package.planName === "Silver"
            ? "ring-[#95A1AF]"
            : props.package.planName === "Platinum"
            ? "ring-[#CA0B0B]"
            : ""
        )}
      >
        {/* Image and Package Name Container */}
        <div className="w-2/5 h-full relative pr-1 truncate flex-shrink-0 flex items-center">
          <Image
            src={NEXT_PUBLIC_IMAGE_URL + props.package.packageImg[0]}
            fill
            alt={props.package.packageName}
            className="object-cover bg-black rounded-tl-md rounded-bl-md"
          />
          <div className="absolute inset-0 bg-black opacity-20 to-transparent rounded-tl-md rounded-bl-md"></div>
          <div
            className="absolute right-0 bottom-0 px-2 py-1 text-white rounded-tl-lg text-[10px] font-Poppins not-italic font-semibold leading-normal tracking-[0.09px]"
            style={{
              border: "1px solid rgba(255, 255, 255, 0.33)",
              background: "rgba(249, 249, 249, 0.38)",
              backdropFilter: "blur(7.599999904632568px)",
            }}
          >
            {props.package.noOfNight}N/ {props.package.noOfDays}D
          </div>
          <div
            className={cn(
              "absolute top-0 left-0 flex justify-center shadow-goldShadow px-2 py-1 text-xs bg-goldGradient text-white rounded-tl-md rounded-br-md",
              props.package.planName === "Gold"
                ? "bg-goldGradient"
                : props.package.planName === "Silver"
                ? "bg-silverGradient"
                : props.package.planName === "Platinum"
                ? "bg-platinumGradient"
                : ""
            )}
          >
            {props.package.planName}
          </div>
        </div>

        {/* Package Name and Others Container */}
        <div className="w-3/5 p-2">
          <div className="flex flex-col h-full justify-between">
            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center ">
                  <h1 className="text-sm flex-shrink text-[#1EC089] font-semibold truncate-2 drop-shadow-sm">
                    {" "}
                    {props.package.packageName}
                  </h1>
                </div>
              </div>
              <div className="flex justify-between pt-1 items-center">
                <div className="flex items-center flex-wrap w-36 truncate-2">
                  {props.package.destination?.map(
                    (d) =>
                      d.noOfNight !== 0 && (
                        <div key={d._id}>
                          <h1 className="text-xs text-[#FF7865] font-semibold mr-2">
                            {d.destinationName} - {d.noOfNight}N
                          </h1>
                        </div>
                      )
                  )}
                </div>
                <h1 className="text-[10px] bg-green-100 rounded-lg px-2 py-1 flex flex-nowrap flex-row whitespace-nowrap items-center leading-none  ">
                  {" "}
                  Starts <span className="text-[9px] pl-1 mb-[3px]"> @</span>
                  <span className="text-[#1EC089] text-[10px]">
                    {props.package.startFrom}
                  </span>{" "}
                </h1>
              </div>
            </div>

            <div className="flex justify-between mt-3 items-center">
              <div className="flex items-center gap-2 text-neutral-500">
                <div className="flex flex-col items-center whitespace-nowrap gap-1">
                  <Building size={16} />
                  <h1 className="text-[8px]">
                    {props.package.hotelCount}{" "}
                    {props.package.hotelCount > 1 ? "Hotels" : "Hotel"}
                  </h1>
                </div>
                <div className="flex flex-col whitespace-nowrap items-center gap-1">
                  <CarFront size={16} />
                  <h1 className="text-[8px]">
                    {props.package.vehicleCount}{" "}
                    {props.package.vehicleCount > 1 ? "Cabs" : "Cab"}
                  </h1>
                </div>
                <div className="flex flex-col whitespace-nowrap items-center gap-1">
                  <LuDices className="" size={16} />
                  <h1 className="text-[8px]">
                    {props.package.activityCount} Activity
                  </h1>
                </div>
              </div>
              <div className="flex flex-col items-end flex-shrink-0 pr-2">
                <div className="text-[#FF7865] flex items-center text-[16px] mr-[5px] font-semibold leading-[21.6px] tracking-[0.48px] font-montserrat">
                  <h1 className="flex items-center ">
                    <IndianRupee size={15} strokeWidth={3} />
                    {formatIndianNumber(price)}
                  </h1>
                </div>
                <h1 className="text-neutral-500 font-semibold text-[10px] text-center mr-1">
                  Per Person
                </h1>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default FilterCardList;

import React, { useState } from 'react';
import { Calendar } from 'lucide-react';

interface DatePanelProps {
  onDateSelect: (date: string) => void;
}

export const DatePanel: React.FC<DatePanelProps> = ({ onDateSelect }) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
    onDateSelect(date.toLocaleDateString());
  };

  // Generate next 30 days
  const generateDates = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }
    
    return dates;
  };

  const dates = generateDates();

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">When?</h3>
      
      <div className="grid grid-cols-7 gap-2 max-h-64 overflow-y-auto">
        {dates.map((date, index) => {
          const isSelected = selectedDate?.toDateString() === date.toDateString();
          const isToday = date.toDateString() === new Date().toDateString();
          
          return (
            <button
              key={index}
              onClick={() => handleDateClick(date)}
              className={`
                p-2 text-center rounded-lg transition-colors text-sm
                ${isSelected 
                  ? 'bg-search-accent text-white' 
                  : isToday 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'hover:bg-gray-100'
                }
              `}
            >
              <div className="font-medium">{date.getDate()}</div>
              <div className="text-xs opacity-75">
                {date.toLocaleDateString('en', { weekday: 'short' })}
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

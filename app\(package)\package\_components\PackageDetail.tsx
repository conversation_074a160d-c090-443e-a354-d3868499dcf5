import React, { useEffect } from "react";
import Inclusions from "./Inclusions";
import Exclusions from "./Exclusions";
import { BookingOverview } from "./BookingOverview";
import PackageHighlight from "./PackageHighlight";
import HotelData from "./HotelData";
import IternaryStory from "./IternaryStory";
import Book from "./Book";
import Cab from "./Cab";
import PackageImage from "./PackageImage";
import { PackType } from "@/app/types/pack";
import { useDispatch, useSelector } from "react-redux";
import { setActivity } from "@/app/store/features/activitySlice";
import { useAppSelector } from "@/app/store/store";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";

interface PropsType {
  pack: PackType;
}

const PackageDetail = ({ pack }: PropsType) => {
  const roomCapacityData = useSelector(
    (store: any) => store.roomSelect.room.totalRooms
  );
  const roomCapacityAdults = useSelector(
    (store: any) => store.roomSelect.room.totalAdults
  );
  const roomCapacityChild = useSelector(
    (store: any) => store.roomSelect.room.totalChilds
  );
  const packageTheme = useAppSelector((state) => state.themeSelect.theme);
  const dispatch = useDispatch();

  useEffect(() => {
    const data = {
      packageName: pack?.packageName,
      packageId: pack?.packageId,
      activity: pack?.activity,
    };
    dispatch(setActivity(data));
  }, [pack, dispatch]);

  return (
    <div className="flex flex-col items-center w-full ">
      {/* Use optional chaining on array index and provide safe fallbacks */}
      <PackageImage img={pack?.packageImg?.[0] ?? ""} name={pack?.packageName ?? ""} />
      <div className="lg:flex contents relative px-5">
        <div className="flex flex-col px-5 rounded-[36px] border border-white top-[240px] bg-white w-full lg:w-1/2 absolute lg:static lg:flex-1">
          <div className="hidden lg:block relative w-[90%] h-[450px]">
            <img
              src={`${NEXT_PUBLIC_IMAGE_URL}${pack?.packageImg?.[0] ?? ""}`}
              alt={pack?.packageName ?? ""}
              className="w-full h-full object-cover rounded-xl"
            />
            <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/60 to-transparent rounded-b-xl">
              <p className="text-white font-semibold text-3xl drop-shadow-2xl">
                {pack?.packageName}
              </p>
            </div>
          </div>
          <PackageHighlight
            plan={pack?.planName}
            destinations={pack?.destination}
            noOfDays={pack?.noOfDays}
            noOfNights={pack?.noOfNight}
            totalAdult={roomCapacityAdults}
            totalChild={roomCapacityChild}
            hotelCount={pack?.hotelCount}
            startsFrom={pack?.startFrom}
          />
          <div className="grid grid-cols-1 w-full lg:w-3/4 lg:px-8">
            {pack?.hotelMeal?.map((hotel, index) => (
              <HotelData key={index} hotel={hotel} index={index + 1} />
            ))}
          </div>
          <Inclusions inclusions={pack?.inclusionDetail} />
          <Exclusions exclusions={pack?.exclusionDetail} />
          <div className="lg:flex lg:px-5 lg:gap-10 lg:justify-start">
            <IternaryStory
              destinations={pack?.destination}
              activity={pack?.activity}
            />
            {pack?.vehicleDetail?.map((vehicle, index) => (
              <Cab key={index} vehicle={vehicle} />
            ))}
          </div>
          <BookingOverview
            packageName={pack?.packageName}
            theme={packageTheme}
            startsFrom={pack?.fullStartDate}
            endsOn={pack?.fullEndDate}
            adults={roomCapacityAdults}
            child={roomCapacityChild}
            room={roomCapacityData}
            hotel={pack?.hotelMeal}
            vehicle={pack?.vehicleDetail}
          />
        </div>
        <div className="lg:w-[35%] lg:sticky lg:top-4 lg:h-fit">
          <Book
            packageId={pack?.packageId}
            price={pack?.finalPackagePrice}
            adult={roomCapacityAdults}
            child={roomCapacityChild}
            gstPer={pack?.gstPer}
            gstPrice={pack?.gstPrice}
            packagePrice={pack?.totalPackagePrice}
          />
        </div>
      </div>
    </div>
  );
};

export default PackageDetail;

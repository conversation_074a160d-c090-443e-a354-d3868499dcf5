import DesktopNavBar from './components/navbar/NavBar';
import Header from './components/navbar/Header';
import NavBar from './components/navbar/MobileNavBar';
import { Providers } from '../providers/SessionProvider';

const Layout = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <Providers>
        <Header />
        <DesktopNavBar />
        <NavBar />
        {children}
      </Providers>
    </>
  );
};

export default Layout;

import Image from "next/image"
import { Pencil,Check } from "lucide-react"
export default function CabSelected ()
{
    return(
        <div className="  mb-[39px] ">
         <div className=" flex flex-col items-center mt-[16px] h-[160px] w-[312px] rounded-[14px] bg-white border-[1.5px] border-[#ACA19F57] shadow-changeCabSelectContShadow ">
             <div className="flex ">
             <Image src='/Cab.svg' height={30} width={87} alt="Cab" className="mx-[23px] my-[34px]" />
             <div >
                 <p className="mt-[13px] w-[113px] h-[21px] text-[14px] text-[#6A778B] font-semibold leading-normal tracking-[0.14px]" style={{"textShadow":"2px 4px 8.4px rgba(51, 214, 159, 0.12)"}}><PERSON><PERSON><PERSON>, Marazzo</p>
                 <p className="w-[86px] h-[15px] text-[#5D6670] text-[10px] font-normal leading-normal bg-clip-text text-transparent " style={{"backgroundImage":"linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)"}}>Private SUV - AC</p>
                 <p className=" mt-[11px] w-[140px] h-[12px] text-[8px] font-medium leading-normal bg-clip-text text-transparent" style={{"backgroundImage":"linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)"}}>7 Sightseeing Transfers Included</p>
                 <div className="flex mt-[6px] ">
                     <div className="flex">
                         <Image src='/Seat.svg' width={12} height={12} alt="seat" />
                         <p className=" ml-[5px] w-[60px] h-[12px] text-[#5D6670] text-[8px] font-medium ">4 Seater</p>
                     </div>
                     <div className="flex">
                         <Image src='/Luggage.svg' width={12} height={12} alt="seat" />
                         <p className=" ml-[5px] w-[60px] h-[12px] text-[#5D6670] text-[8px] font-medium ">2 Luggage</p>
                     </div>
                 </div>
            </div>
            </div>
        <div>
          <hr className="w-[278px] border-changeCabLine" />
        </div>

        <div className="relative ml-[176px] mt-[14px] flex items-center">
                <Image src="/Plus.svg" height={5} width={5} alt="plus" className="mr-[3px] "/>
                <Image src="/Rupees.svg" height={5} width={5} alt="rupees" className="mr-[2px]"/>
                
                <p className="h-[15px] w-[37px] font-montserrat text-[12px] font-semibold leading-[16.2px] tracking-[0.36px] bg-clip-text text-transparent " style={{"backgroundImage":" linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)"}} >8,999</p>
                <p className="absolute bottom-0 right-[70px] text-[#6A778B] text-[4px] text-normal leading-normal tracking-[0.04px]">per person</p>
                <p className="inline-flex items-center px-[14px] py-[3.5px] rounded-lg border border-[#27B182] shadow-changeHotelSelectShadow text-[10px] font-medium leading-normal tracking-[0.1px] text-transparent bg-clip-text " style={{"backgroundImage":"linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)"}}>Select</p>
            </div>
     </div>
     </div>
    )
}
import React from "react";
import { MapPin } from "lucide-react";
import { FaLocationDot, FaUser } from "react-icons/fa6";
import Image from "next/image";

const MyIternary = () => {
  const iternaryDetail = [
    {
      label: "Enchanting Manali",
      location: "Shimla, Manali",
      Person: "2+1",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      duration: "5 Days",
    },
    {
      label: "Enchanting Ooty",
      location: "Ooty, Tamil Nadu",
      Person: "4+1",
      image: "https://images.unsplash.com/photo-1544735716-392fe2489ffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      duration: "4 Days",
    },
    {
      label: "Enchanting Kashmir",
      location: "Srinagar, Kashmir",
      Person: "4+1",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      duration: "6 Days",
    },
    {
      label: "Enchanting Shimla",
      location: "Shimla, Himachal",
      Person: "4+1",
      image: "https://images.unsplash.com/photo-1571115764595-644a1f56a55c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      duration: "3 Days",
    },
  ];

  if (iternaryDetail.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-6">
          <MapPin className="w-10 h-10 text-gray-400" />
        </div>
        <h3 className="text-xl font-semibold text-gray-700 mb-2">No Itineraries Yet</h3>
        <p className="text-gray-500 max-w-md">Start planning your dream vacation and create your first itinerary!</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {iternaryDetail.map((data, index) => (
        <div
          key={index}
          className="group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100 overflow-hidden cursor-pointer"
        >
          {/* Image Container */}
          <div className="relative h-40 overflow-hidden">
            <Image
              src={data.image}
              alt={data.label}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-300"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />

            {/* Duration Badge */}
            <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1">
              <span className="text-xs font-semibold text-gray-700">{data.duration}</span>
            </div>
          </div>

          {/* Content */}
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2 bg-gradient-to-r from-[#FF5F5F] to-[#FF9080] bg-clip-text text-transparent line-clamp-1">
              {data.label}
            </h3>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <FaLocationDot className="w-4 h-4 text-emerald-500 flex-shrink-0" />
                <span className="text-sm text-gray-600 font-medium truncate">
                  {data.location}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <FaUser className="w-4 h-4 text-[#FF5F5F] flex-shrink-0" />
                <span className="text-sm text-gray-600 font-medium">
                  {data.Person} Travelers
                </span>
              </div>
            </div>

            {/* Action Button */}
            <div className="mt-4 pt-3 border-t border-gray-100">
              <button className="w-full bg-gradient-to-r from-[#FF5F5F] to-[#FF9080] text-white py-2 px-4 rounded-lg font-medium text-sm hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                View Details
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MyIternary;
